<template>
  <div :class="{ 'has-logo': showLogo }">
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :default-openeds="open_list"
        :collapse="isCollapse"
        :background-color="variables.menuBg"
        :text-color="variables.menuText"
        :unique-opened="false"
        :active-text-color="variables.menuActiveText"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item
          v-for="route in permission_routes"
          :key="route.path"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Logo from "./Logo";
import SidebarItem from "./SidebarItem";
import variables from "@/styles/variables.scss";

export default {
  components: { SidebarItem, Logo },
  data() {
    return {
      open_list: ["/resourcedetect", "/tabledetect", "/permission"], // 默认打开的菜单栏
    };
  },
  mounted() {
    // 动态添加项目管理菜单到默认打开列表
    const projectId = this.$route.params.projectId;
    if (projectId) {
      const projectPath = `/project/${projectId}`;
      if (!this.open_list.includes(projectPath)) {
        this.open_list.push(projectPath);
      }
    }
  },
  watch: {
    '$route'(to) {
      // 监听路由变化，动态更新打开列表
      const projectId = to.params.projectId;
      if (projectId) {
        const projectPath = `/project/${projectId}`;
        if (!this.open_list.includes(projectPath)) {
          this.open_list.push(projectPath);
        }
      }
    }
  },
  computed: {
    ...mapGetters(["permission_routes", "sidebar"]),
    activeMenu() {
      const route = this.$route;
      const { meta, path } = route;
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    },
    showLogo() {
      // return this.$store.state.settings.sidebarLogo
      return true;
    },
    variables() {
      return variables;
    },
    isCollapse() {
      return !this.sidebar.opened;
    },
  },
};
</script>
