<template>
  <div>
    <div>
      <div style="display: flex">
        <!-- 规则列表查看 -->
        <el-popconfirm
          :title="$t('projectDetail.isGoRulesList')"
          placement="top-start"
          @confirm="goRuleList"
        >
          <el-button
            v-if="isRulesListPermissions()"
            type="success"
            icon="el-icon-s-promotion"
            slot="reference"
            >{{ $t("projectDetail.viewRulesList") }}</el-button
          >
        </el-popconfirm>
        <!-- 对比报告 -->
        <el-button
          style="margin-left: 8px"
          type="primary"
          icon="el-icon-magic-stick"
          @click="handleCorrelationData"
          >{{ $t("projectDetail.compareReports") }}</el-button
        >
        <!-- 扫描报告 -->
        <el-button
          style="margin-left: 8px"
          type="primary"
          icon="el-icon-thumb"
          @click="handleScan"
          >{{ $t("projectDetail.startScan") }}</el-button
        >
      </div>
      <!-- 扫描弹窗 -->
      <el-dialog
        :title="$t('projectDetail.reportScan')"
        :visible.sync="dialogVisible"
        width="35%"
      >
        <el-button
          v-if="!isScan"
          size="small"
          type="primary"
          @click="startScan"
          >{{ $t("projectDetail.startScan") }}</el-button
        >
        <div v-else class="scan-time">
          <div>{{ $t("projectDetail.scanProgress") }}：</div>
          <el-progress
            style="margin: 8px 0 15px 0"
            :text-inside="true"
            :stroke-width="20"
            :percentage="percentage"
            status="success"
          ></el-progress>
          <div>
            {{ $t("projectDetail.scanTime") }}：<span>{{ time }}</span>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">{{
            $t("projectDetail.ok")
          }}</el-button>
        </span>
      </el-dialog>

      <el-table
        :data="
          tableData.filter(
            (data) => !searchValue || data.id === Number(searchValue)
          )
        "
        ref="multipleTable"
        row-key="id"
        style="width: 100%; margin-top: 20px"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="55"
          reserve-selection
          :selectable="selectable"
        ></el-table-column>
        <el-table-column :label="$t('projectDetail.testId')" prop="id" />
        <el-table-column
          :label="$t('projectDetail.testVersion')"
          prop="branch"
        />
        <el-table-column
          :label="$t('projectDetail.dateTime')"
          prop="scan_time"
        />
        <el-table-column
          :label="$t('projectDetail.errorCount')"
          prop="errorTotal"
        />
        <el-table-column align="right">
          <template slot="header">
            <el-input
              v-model="searchValue"
              size="mini"
              :placeholder="$t('projectDetail.searchTestId')"
            />
          </template>
          <template slot-scope="scope">
            <el-button plain size="mini" @click="goDetail(scope.row)">{{
              $t("projectDetail.accessReport")
            }}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页器 -->
    <div style="display: flex; justify-content: end">
      <Pagination
        style="margin-top: 10px; padding: 10px 20px"
        :total="reportListTotal"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="pageSwitch"
        :auto-scroll="listQuery.limit >= 30"
      />
    </div>
    <!-- 返回顶部组件 -->
    <el-tooltip placement="top" :content="$t('backToTop')">
      <back-to-top
        :visibility-height="300"
        :back-position="50"
        transition-name="fade"
      ></back-to-top>
    </el-tooltip>
  </div>
</template>

<script>
import moment from "moment";
import Pagination from "@/components/Pagination/index.vue";
import BackToTop from "@/components/BackToTop";
import { addTask, getScanResult } from "@/api/project";

export default {
  components: { Pagination, BackToTop },
  props: ["reportList", "reportListTotal"],
  name: "ReportList",
  data() {
    return {
      tableData: [],
      listQuery: {
        page: 1,
        limit: 10,
      },
      multipleSelection: [],
      selectMax: 2,
      searchValue: "", //搜索关键词
      ProjectVerId: null,

      dialogVisible: false,
      isScan: false,
      beginTime: "",
      endTime: "",
      timer: "",
      time: "00分00秒",
      percentage: 0,
      aveScanTime: 60, //平均扫描用时(秒)
      judgeNum: 10, //判断百分数
    };
  },
  computed: {
    permission_routes() {
      return this.$store.getters.permission_routes; //系统语言
    },
    sessionDuration: function () {
      clearInterval(this.timer); //清除计时器
      if (this.endTime) {
        //已结束
        let date = this.endTime - this.beginTime;
        this.time = this.formateSeconds(date);
      } else if (this.beginTime) {
        //添加计时器，每一秒计时一次
        this.timer = setInterval(() => {
          //当前时间-开始时间
          let date = moment().format("X") - this.beginTime;
          this.time = this.formateSeconds(date);
          if (date < this.aveScanTime) {
            this.percentage = Math.round((date / this.aveScanTime) * 100);
          } else {
            this.handleScan();
          }
        }, 1000);
      }
    },
  },
  watch: {
    percentage(val) {
      if (val < 5) {
        this.handleScan();
        return;
      }
      if (val === this.judgeNum) {
        this.handleScan();
        this.judgeNum += 10;
      }
    },
    reportList(newval, oldval) {
      this.tableData = this.reportList;
    },
    sessionDuration(a, b) {},
  },
  mounted() {
    if (localStorage.getItem("scan_begin_time")) {
      this.isScan = true;
      this.beginTime = localStorage.getItem("scan_begin_time");
    }
  },
  methods: {
    // 判断是否有跳转规则列表页面的按钮权限
    isRulesListPermissions() {
      let data = [];
      this.permission_routes.forEach((item) => {
        if (item.path === "/resourcedetect") data = item.children;
      });
      return data.some((item) => item.path === "rulelist");
    },
    // 前往规则列表查看
    goRuleList() {
      localStorage.setItem(
        "rule_projectId",
        JSON.stringify(this.$route.query.projectId)
      );
      localStorage.setItem(
        "rule_projectBranch",
        JSON.stringify(this.$route.query.branch)
      );
      this.$router.push("/resourcedetect/rulelist");
    },
    // 获取对应行id
    goDetail(row) {
      // 路由传递对应报告id 后续报告页通过this.$route.query.id请求报告数据
      this.$router.push({
        path: "/prodetail/reportdetail",
        query: {
          projectId: this.$route.query.projectId,
          branch: this.$route.query.branch,
          reportId: row.id,
          keyword: "",
        },
      });
    },

    // 超过两个选中其余选项禁选
    selectable(row, index) {
      let isSelected = this.$refs.multipleTable.selection.includes(row);
      return (
        !(
          this.selectMax > 0 &&
          this.$refs.multipleTable.selection.length >= this.selectMax
        ) || isSelected
      );
    },
    // 选中的数据
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 对比选择的数据
    handleCorrelationData() {
      if (this.multipleSelection.length === 0) {
        this.$message({
          message: this.$t("projectDetail.selectTwoReport"),
          type: "warning",
        });
        return;
      }
      if (this.multipleSelection.length === 1) {
        this.$message({
          message: this.$t("projectDetail.selectAnotherReport"),
          type: "warning",
        });
        return;
      }

      let reportId1 = this.multipleSelection[0].id;
      let reportId2 = this.multipleSelection[1].id;
      // 获取id去后台获取到数据,然后展示对比的数据
      this.$router.push({
        path: "/prodetail/reportcomparison",
        query: {
          projectId: this.$route.query.projectId,
          branch: this.$route.query.branch,
          id1: reportId1,
          id2: reportId2,
        },
      });
    },
    // 操作扫描
    async handleScan() {
      this.dialogVisible = true;
      if (!this.isScan) return;
      let params = {
        projectId: this.$route.query.projectId,
        branch: this.$route.query.branch,
      };
      let data = await getScanResult(params);
      // console.log(data);
      this.percentage = data.percentage;
      if (data.code === 232) {
        this.$message.error(data.msg);
        this.isScan = false;
        this.hangUp();
      }
      if (data.code === 233) {
        this.isScan = false;
        this.hangUp();
      }
      // this.isScan = true;
      // this.answer();
    },
    // 开始扫描
    async startScan() {
      this.percentage = 0;
      let params = {
        projectId: this.$route.query.projectId,
        branch: this.$route.query.branch,
      };
      let { code, data } = await addTask(params);
      // console.log(data.test);
      this.aveScanTime = data.test.ave;
      // this.beginTime = data.test.start
      this.isScan = true;
      this.answer();
    },
    // 完成扫描
    finishScan() {
      if (localStorage.getItem("scan_end_time")) {
        this.endTime = localStorage.getItem("scan_end_time");
        localStorage.removeItem("scan_begin_time");
      } else {
        this.hangUp();
      }
    },
    //计时开始方法
    answer() {
      this.endTime = "";
      this.beginTime = moment().format("X"); //点击开始，获取当前时间(秒)
      localStorage.setItem("scan_begin_time", this.beginTime);
      localStorage.removeItem("scan_end_time");
    },
    // 计时结束方法
    hangUp() {
      this.judgeNum = 10;
      this.endTime = moment().format("X"); //点击结束，获取当前时间(秒)
      localStorage.setItem("scan_end_time", this.endTime);
      localStorage.removeItem("scan_begin_time");
    },
    //将秒转化为时分秒
    formateSeconds(endTime) {
      let secondTime = parseInt(endTime); //将传入的秒的值转化为Number
      let min = 0; // 初始化分
      let h = 0; // 初始化小时
      let result = "";
      if (secondTime > 60) {
        //如果秒数大于60，将秒数转换成整数
        min = parseInt(secondTime / 60); //获取分钟，除以60取整数，得到整数分钟
        secondTime = parseInt(secondTime % 60); //获取秒数，秒数取佘，得到整数秒数
        if (min > 60) {
          //如果分钟大于60，将分钟转换成小时
          h = parseInt(min / 60); //获取小时，获取分钟除以60，得到整数小时
          min = parseInt(min % 60); //获取小时后取佘的分，获取分钟除以60取佘的分
        }
      }
      if (h.toString().padStart(2, "0") == "00") {
        result = `${min.toString().padStart(2, "0")}${
          this.$t("projectDetail.min") +
          secondTime.toString().padStart(2, "0") +
          this.$t("projectDetail.second")
        }`;
      } else {
        result = `${h.toString().padStart(2, "0")}${
          this.$t("projectDetail.hour") + min.toString().padStart(2, "0")
        }${
          this.$t("projectDetail.min") +
          secondTime.toString().padStart(2, "0") +
          this.$t("projectDetail.second")
        }`;
      }

      return result;
    },

    // 分页器页码切换 获取页码和页数
    pageSwitch(val) {
      this.$parent.getReportList(this.listQuery);
    },
  },
};
</script>

<style lang="scss" scoped>
/* 禁用全选 */
::v-deep .el-table__header-wrapper .el-checkbox {
  display: none;
}

.scan-time {
  div {
    font-size: 16px;
  }
}
</style>
