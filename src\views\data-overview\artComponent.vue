<template>
  <div class="app-container">
    <div class="pro-detail">
      <div>
        <el-descriptions :title="$t('projectDetail.projectDetail')" :column="2" style="font-size: 16px">
          <el-descriptions-item :label="$t('projectDetail.projectName')">{{
            projectName
          }}</el-descriptions-item>
          <!-- <el-descriptions-item label="最新版本">{{
            proStat.branch
          }}</el-descriptions-item> -->
          <!-- <el-descriptions-item label="测试版本">{{
            proStat.branch
          }}</el-descriptions-item> -->
          <el-descriptions-item :label="$t('projectDetail.lastScanned')">
            {{ proStat.scan_time }}</el-descriptions-item>
        </el-descriptions>
        <el-divider />
        <el-row :gutter="32">
          <el-col :xs="24" :sm="24" :lg="24" :xl="14">
            <div>
              <el-tabs v-model="activeName" @tab-click="pieChartChange">
                <el-tab-pane :label="$t('projectDetail.totalResources')" name="type" />
                <el-tab-pane :label="$t('projectDetail.totalResourcesSize')" name="size" />
                <el-tab-pane :label="$t('projectDetail.errorCount')" name="error" />
              </el-tabs>
              <!-- 饼状图 -->
              <div v-if="pieChartData.length > 0" style="height: 680px">
                <pie-chart
                  :pieChartData="pieChartData"
                  :title="pieChartTitle"
                ></pie-chart>
              </div>
              <el-empty v-else :image-size="200" />
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="24" :xl="10">
            <!-- 资源类型表 -->
            <div class="chart-wrapper">
              <el-table
                :data="tableData"
                border
                :default-sort="{ prop: 'count', order: 'descending' }"
                max-height="608px"
              >
                <el-table-column
                  prop="type"
                  :label="$t('projectDetail.resourceType')"
                  fixed="left"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="count"
                  :label="$t('projectDetail.total')"
                  sortable
                />
                <el-table-column
                  prop="size_sum"
                  :label="$t('projectDetail.totalResourcesSize') + '(KB)'"
                  sortable
                />
                <el-table-column
                  fixed="right"
                  prop="error_rule_num"
                  :label="$t('projectDetail.errorCount')"
                  sortable
                />
              </el-table>
            </div>
          </el-col>
        </el-row>
      </div>
      <div>
        <el-divider />
        <h4>{{ $t("projectDetail.resourceErrorCount") }}</h4>
        <ErrorLineChart
          :xAxisData="scanTimeList"
          :errorChartData="errorChartData"
          :height="errorChartHeigh"
        />
        <!-- <el-divider />
        <h4>{{ $t("projectDetail.reportList") }}</h4> -->
        <!-- <ReportList :report-list="reportList" :report-list-total="reportListTotal" /> -->
      </div>
    </div>
    <!-- 返回顶部组件 -->
    <!-- <el-tooltip placement="top" :content="$t('backToTop')">
      <back-to-top :visibility-height="300" :back-position="50" transition-name="fade" />
    </el-tooltip> -->
  </div>
</template>

<script>
import BackToTop from '@/components/BackToTop'
import ErrorLineChart from './components/ErrorLineChart.vue'
import ReportList from './components/ReportList.vue'
import { getReportStat, getReportSummary } from '@/api/project'
import { mapGetters, mapActions } from 'vuex'
import PieChart from './components/PieChart.vue'

export default {
  components: {
    BackToTop,
    ReportList,
    ErrorLineChart,
    PieChart,
  },
  name: "Prodetail",
  data() {
    return {
      activeName: 'type', // 饼状图切换数据
      proStat: {}, // 项目报告信息
      tableData: [],
      pieChartData: [],
      pieChartTitle: '',
      resourceTypeData: [], // 资源类型总数
      resourceSizeData: [], // 资源类型总大小
      resourceErrorData: [], // 资源告警数

      scanTimeList: [], // 保存报告列表的扫描时间
      errorChartData: [],
      errorChartHeigh: '',

      reportList: [],
      reportListTotal: 0
    }
  },
  // 在页面离开时记录滚动位置
  beforeRouteLeave(to, from, next) {
    this.scrollTop =
      document.documentElement.scrollTop || document.body.scrollTop
    next()
  },
  // 进入该页面时，用之前保存的滚动位置赋值
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      document.body.scrollTop = vm.scrollTop
    })
  },

  mounted() {
    this.initializeData()
  },
  watch: {
    // 监听路由变化，更新项目ID
    '$route'(to, from) {
      if (to.params.projectId !== from.params.projectId) {
        console.log('项目ID变化，重新获取数据:', to.params.projectId);
        this.initializeData();
      }
    },
    // 监听选中分支变化
    selectedBranch(newBranch, oldBranch) {
      if (newBranch !== oldBranch && newBranch && this.currentProjectId) {
        console.log('分支变化，重新获取数据:', newBranch);
        this.refreshData();
      }
    },
    // 监听选中检查类型变化
    selectedCheckType(newType, oldType) {
      if (newType !== oldType && newType && this.currentProjectId && this.selectedBranch) {
        console.log('检查类型变化，重新获取数据:', newType);
        this.refreshData();
      }
    }
  },
  computed: {
    ...mapGetters(['projectName', 'projectBranch', 'selectedBranch', 'selectedCheckType']),
    // 获取当前项目ID
    currentProjectId() {
      return this.$route.params.projectId || sessionStorage.getItem('currentProjectId')
    }
  },
  methods: {
    ...mapActions('project', ['fetchBranchOptions']),

    // 初始化数据
    async initializeData() {
      // if (!this.currentProjectId) {
      //   console.warn('未找到项目ID');
      //   return;
      // }

      // // 获取分支数据
      // try {
      //   await this.fetchBranchOptions(this.currentProjectId);
      // } catch (error) {
      //   console.error('获取分支数据失败:', error);
      // }

      // 获取报告数据
      this.refreshData();
    },

    // 刷新数据
    refreshData() {
      this.getReportStat();
      this.getTypeError();
      this.getReportList();
    },

    // 获取报告基本数据
    async getReportStat() {
      const projectId = this.currentProjectId;
      const branch = this.selectedBranch || this.$route.query.branch;

      if (!projectId || !branch) {
        console.warn('缺少项目ID或分支信息:', { projectId, branch });
        return;
      }

      const params = { projectId, branch };
      const { code, data } = await getReportStat(params);
      if (code === 200) {
        this.proStat = data.branch;
        this.tableData = data.stat;
        this.resourceTypeData = [];
        this.resourceSizeData = [];
        this.resourceErrorData = [];
        data.stat.forEach((item) => {
          this.resourceTypeData.push({ value: item.count, name: item.type })
          this.resourceSizeData.push({ value: item.size_sum, name: item.type })
          this.resourceErrorData.push({
            value: item.error_rule_num,
            name: item.type
          })
        })
        this.pieChartChange()
      }
    },

    // 获取资源告警数
    async getTypeError() {
      const projectId = this.currentProjectId;
      const branch = this.selectedBranch || this.$route.query.branch;

      if (!projectId || !branch) {
        console.warn('缺少项目ID或分支信息:', { projectId, branch });
        return;
      }

      const params = {
        projectId,
        branch,
        pageNum: 1,
        pageSize: 10,
      };
      const { code, data } = await getReportSummary(params);
      if (code !== 200) return;
      data.reverse(); //反转数据

      let arr = [];
      this.scanTimeList = [];
      data.forEach((item) => {
        arr.push({ type: 'ErrorTotal', count: item.errorTotal })
        arr.push(...item.typeError)
        this.scanTimeList.push(item.scan_time)
      })
      const dataArr = []
      arr.map((items) => {
        const res = dataArr.some((item) => {
          // 判断相同名字，有就添加到当前项
          if (item.type == items.type) {
            item.count.push(items.count)
            return true
          }
        })
        if (!res) {
          // 如果没找相同名字添加一个新对象
          dataArr.push({ type: items.type, count: [items.count] })
        }
      })
      this.errorChartHeigh = dataArr.length + 680 + 'px'
      this.errorChartData = dataArr
    },
    // 获取报告列表
    async getReportList(listQuery) {
      const projectId = this.currentProjectId;
      const branch = this.selectedBranch || this.$route.query.branch;

      if (!projectId || !branch) {
        console.warn('缺少项目ID或分支信息:', { projectId, branch });
        this.goBack();
        return;
      }

      const params = {
        projectId,
        branch,
        pageNum: listQuery ? listQuery.page : 1,
        pageSize: listQuery ? listQuery.limit : 10
      }
      const { code, data, dataLen } = await getReportSummary(params)
      if (code === 200) {
        this.reportList = data
        this.reportListTotal = dataLen
      }
    },

    goBack() {
    //   this.$router.push("/resourcedetect/project");
    },
    // 切换饼状图数据
    pieChartChange() {
      if (this.activeName === 'type') {
        this.pieChartData = this.resourceTypeData
        this.pieChartTitle = this.$t('projectDetail.resourceComposition')
      } else if (this.activeName === 'size') {
        this.pieChartData = this.resourceSizeData
        this.pieChartTitle = this.$t('projectDetail.resourceSize')
      } else {
        this.pieChartData = this.resourceErrorData
        this.pieChartTitle = this.$t('projectDetail.resourceErrorCount')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  .pro-detail {
    background-color: #fff;
    padding: 15px;
    padding-top: 25px;
  }
}

.pie-table {
  display: flex;
  justify-content: flex-start;
}

.chart-wrapper {
  margin-top: 30px;
  margin-bottom: 10px;
}

@media (min-width: 1920px) {
  .chart-wrapper {
    margin-top: 60px;
    margin-bottom: 0;
  }
}

// ----------修改elementui表格的默认样式-----------
::v-deep .el-table__body-wrapper {
  cursor: pointer;

  &::-webkit-scrollbar {
    height: 6px;
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    // 轨道颜色
    background-color: #fff;
  }

  &::-webkit-scrollbar-thumb {
    // 滚动块颜色
    background-color: #e6e9ed;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    // hover高亮
    background-color: #d5d8db;
  }
}

// 解决表格固定列问题
// ::v-deep .el-table__fixed,
// .el-table__fixed-right {
//   height: calc(100% - 7px) !important;
//   box-shadow: -5px -2px 10px rgba(0, 0, 0, 0.12) !important;
//   .el-table__fixed-body-wrapper {
//     height: calc(100% - 36px) !important;
//   }
// }
// 当表格没有滚动条时
::v-deep .el-table__body-wrapper.is-scrolling-none ~ .el-table__fixed-right {
  height: 100% !important;
  bottom: 0 !important;
  box-shadow: none !important;
}

// 当表格有纵向滚动条时
::v-deep .el-table--scrollable-y .el-table__fixed-right {
  right: 6px !important;
}

// // 当表格只有横向滚动条，没有纵向滚动条时
::v-deep
  .el-table--scrollable-x:not(.el-table--scrollable-y)
  .el-table__fixed-right {
  right: 0 !important;
}
</style>
