<template>
  <div class="app-container">
    <div class="rule-form">
      <el-page-header
        :content="$t('ruleEdit.ruleEdit')"
        :title="$t('ruleEdit.backToRulesList')"
        @back="$router.back()"
      />
      <el-divider />
      <el-form ref="form" :model="form" :rules="rules" label-width="95px">
        <el-form-item :label="$t('ruleCreate.projectBranch')">
          <div style="display: flex; justify-content: space-between">
            <span style="font-size: 16px">{{
              form.projectName + "：" + form.projectBranch
            }}</span>
            <el-radio-group
              v-model="form.isEnabled"
              :fill="form.isEnabled ? '#13ce66' : '#F56C6C'"
            >
              <el-radio-button :label="true">{{
                form.isEnabled
                  ? $t("ruleCreate.enabled")
                  : $t("ruleCreate.enable")
              }}</el-radio-button>
              <el-radio-button :label="false">{{
                form.isEnabled
                  ? $t("ruleCreate.ignore")
                  : $t("ruleCreate.ignored")
              }}</el-radio-button>
            </el-radio-group>
          </div>
        </el-form-item>
        <el-form-item :label="$t('ruleCreate.ruleCategories')">
          <span style="font-size: 16px; margin-right: 10px">{{
            form.stype
          }}</span>
          <el-tooltip
            class="item"
            effect="dark"
            :content="'更改优先级'"
            placement="top"
          >
            <el-select
              v-model="form.ruleLevel"
              name="ruleLevel"
              :placeholder="'更改优先级'"
              style="width: 120px"
            >
              <el-option label="高" value="high"></el-option>
              <el-option label="中" value="middle"></el-option>
              <el-option label="低" value="low"></el-option>
            </el-select>
          </el-tooltip>
        </el-form-item>
        <el-form-item :label="$t('ruleCreate.ruleName')" prop="name">
          <el-input
            v-model="form.name"
            :placeholder="$t('ruleCreate.enterName')"
            :clearable="true"
            style="width: 250px"
          />
        </el-form-item>
        <el-form-item :label="$t('ruleCreate.conditionParameter')">
          <div>
            <el-button
              v-if="rulesForm.conditions.length === 0"
              icon="el-icon-plus"
              type="primary"
              size="mini"
              style="margin-left: 0"
              @click="addFirstConditionGroup('condition')"
              >{{ $t("ruleCreate.addConditionGroup") }}</el-button
            >
            <Conditions v-else :conditions="rulesForm.conditions" />
          </div>
        </el-form-item>
        <el-form-item
          :label="$t('ruleCreate.targetParameter')"
          :rules="{ required: true }"
        >
          <div>
            <el-button
              v-if="rulesForm.asserts.length === 0"
              icon="el-icon-plus"
              type="primary"
              size="mini"
              style="margin-left: 0"
              @click="addFirstConditionGroup('assert')"
              >{{ $t("ruleCreate.addConditionGroup") }}</el-button
            >
            <Conditions v-else :conditions="rulesForm.asserts" />
          </div>
        </el-form-item>

        <el-form-item size="large">
          <div style="display: flex; margin-top: 15px">
            <el-button type="info" @click="testRule"
              >{{ $t("ruleCreate.testRule")
              }}<i class="el-icon-document-checked el-icon--right"></i
            ></el-button>
            <el-button type="primary" @click="onDownload"
              >{{ $t("ruleCreate.downloadRule")
              }}<i class="el-icon-download el-icon--right"></i
            ></el-button>
            <el-button type="success" @click="onSubmit"
              >{{ $t("ruleCreate.saveRule")
              }}<i class="el-icon-circle-check el-icon--right"></i
            ></el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <!-- 测试规则弹窗展示数据 -->
    <test-rule-modal
      projectType="resource"
      :testRuleVisible="testRuleVisible"
      :testRuleData="testRuleData"
      @closeTestRule="closeTestRule"
    ></test-rule-modal>
  </div>
</template>

<script>
import { deepClone } from "@/utils";
import { isArray } from "@/utils/validate";
// 引入导出JSON文件的插件
import FileSaver from "file-saver";
import { mapGetters } from "vuex";
import Conditions from "./componets/Conditions.vue";
import TestRuleModal from "@/components/TestRuleModal/index.vue";
import { getRule, updateRule, testRule, getRuleConfig } from "@/api/rule";

export default {
  components: { Conditions, TestRuleModal },

  data() {
    return {
      key: Date.now(), //条件绑定的key
      checkConditionsValue: true, // 保存验证表单条件数据是否通过
      checkAssertsValue: true, // 保存验证表单条件数据是否通过

      ruleData: {}, //保存后台请求的当前规则数据
      // 保存表单的数据
      form: {
        projectId: "",
        projectName: "",
        projectBranch: "",
        stype: "",
        ruleLevel: "middle",
        name: "",
        isEnabled: true,
      },
      rules: {
        //规则类型和名称的验证规则
        projectId: [
          {
            required: true,
            message: this.$t("ruleCreate.projectBranchRequired"),
            trigger: "change",
          },
        ],
        stype: [
          {
            required: true,
            message: this.$t("ruleCreate.ruleCategoriesRequired"),
            trigger: "change",
          },
        ],
        name: [
          {
            required: true,
            message: this.$t("ruleCreate.ruleNameRequired"),
            trigger: "blur",
          },
          {
            pattern: /^[^\?\:\*\"\<\>\|\/\\\？\：\“\”]*$/,
            message: '名称不能包含\\ / : * ? " < > |',
            trigger: "change",
          },
        ],
      },

      testRuleVisible: false, //测试规则弹窗
      testRuleData: [],
    };
  },
  computed: {
    ...mapGetters(["rulesForm"]),
  },
  // 离开编辑页面初始化规则数据
  beforeRouteLeave(to, form, next) {
    let ruleData = {
      stype: "",
      ruleLevel: "middle",
      isEnabled: true,
      name: "",
      condition: {},
      assert: {},
    };
    this.$store.commit("conditions/setUploadData", ruleData);
    next();
  },
  mounted() {
    this.getRuleData();
    this.form.stype = this.rulesForm.stype;
    this.form.ruleLevel = this.rulesForm.ruleLevel;
    this.form.name = this.rulesForm.name;
    this.form.isEnabled = this.rulesForm.isEnabled;
  },
  // 监控表单数据变化
  watch: {
    "rulesForm.stype"(newValue, oldValue) {
      this.form.stype = newValue;
    },
    "rulesForm.ruleLevel"(newValue, oldValue) {
      this.form.ruleLevel = newValue;
    },
    "rulesForm.name"(newValue, oldValue) {
      this.form.name = newValue;
    },
    "rulesForm.isEnabled"(newValue, oldValue) {
      this.form.isEnabled = newValue;
    },
  },

  methods: {
    // 获取规则分类
    async getRuleConfig(projectId, branch, stype) {
      let params = {
        projectId,
        branch,
      };
      let { code, json } = await getRuleConfig(params);
      if (code === 200) {
        this.$store.commit("conditions/setRuleConfig", json);
        let stypeIndex = (json || []).findIndex((item) => item.type === stype);
        this.$store.commit("conditions/setStypeIndex", stypeIndex); //把获取的类型索引值保存
        return true;
      } else {
        return false;
      }
    },
    // 获取规则数据
    async getRuleData() {
      let ruleId = this.$route.query.ruleId;
      if (!ruleId) {
        this.$router.push("/rulelist/index");
        return;
      }
      let { code, data, info } = await getRule({ ruleId });
      let ruleData = {};
      if (code === 200) {
        // 先获取规则分类
        await this.getRuleConfig(info.projectId, info.branch, info.stype);
        this.form.projectId = info.projectId;
        this.form.projectName = info.projectName;
        this.form.projectBranch = info.branch;
        ruleData = data;
      }

      let key = Date.now();
      if (!ruleData.condition.type) {
        ruleData.condition = {};
      } else {
        // 调用处理数据的方法
        this.handleRuleData([ruleData.condition], key);
      }
      if (!ruleData.assert.type) {
        // console.log("空的");
        ruleData.assert = {};
      } else {
        // 调用处理数据的方法
        this.handleRuleData([ruleData.assert], key);
      }
      this.ruleData = deepClone(ruleData);
      // 检查获取的对象数据是否有ruleLevel 没有就添加默认值为middle
      if (!this.ruleData.hasOwnProperty("ruleLevel")) {
        this.ruleData.ruleLevel = "middle";
      }
      // 调用vuex导入的mutation方法更新vuex数据
      this.$store.commit("conditions/setUploadData", this.ruleData);
    },
    // 处理后台获取的规则数据
    handleRuleData(data, key) {
      for (const item of data) {
        if (
          item.type === "group" &&
          item.prefix === "true" &&
          item.operator === "and"
        ) {
          item.operator = "and";
          if (!item.key) {
            // console.log("没有key");
            item.key = key--;
          }
        }
        if (
          item.type === "group" &&
          item.prefix === "true" &&
          item.operator === "or"
        ) {
          item.operator = "or";
          if (!item.key) {
            item.key = key--;
          }
        }
        if (
          item.type === "group" &&
          item.prefix === "false" &&
          item.operator === "and"
        ) {
          item.operator = "not and";
          if (!item.key) {
            item.key = key--;
          }
        }
        if (
          item.type === "group" &&
          item.prefix === "false" &&
          item.operator === "or"
        ) {
          item.operator = "not or";
          if (!item.key) {
            item.key = key--;
          }
        }

        if (item.type === "meta") {
          if (!item.key) {
            item.key = key--;
            item.stypeIndex = this.stypeIndex;
          }
          if (item.operator === "in") {
            // 修改前：根据类型处理数据
            // if (item.operatorType === 0) item.value = item.value[0];
            // if (item.operatorType === 1) item.value = item.value.toString();
            
            // 修改后：将数组转换为逗号分隔的字符串，用于显示在输入框中
            if (Array.isArray(item.value)) {
              item.value = item.value.join(',');
            }
          }
          // if (typeof item.value === "string") {
          //   item.operatorType = 1;
          // } else if (typeof item.value === "number" || isArray(item.value)) {
          //   item.operatorType = 0;
          // } else {
          //   item.operatorType = 2;
          // }
        }

        // 如果item.value存在并且是数组，就再调用一次函数，传数组给他，直到没有value数组结束
        if (item.value && item.value.length > 0) {
          this.handleRuleData(item.value);
        }
      }
    },

    // 当没有数据时先创建初始条件组的自定义事件
    addFirstConditionGroup(createType) {
      // console.log(this.$refs.form.validateField("stype"));
      if (!this.form.stype) {
        this.$refs.form.validateField("stype");
        this.$message({
          type: "warning",
          message: this.$t("ruleCreate.ruleCategoriesRequired"),
        });
      } else {
        this.$store.commit("conditions/addFirstConditionGroup", createType);
      }
    },
    // 获取对应类型所在的索引值 切换类型清空数据
    getStypeIndex(stype, index) {
      if (this.form.stype !== "" && this.form.stype !== stype) {
        this.$confirm(
          this.$t("ruleCreate.switchTypeTips"),
          this.$t("ruleCreate.tips"),
          {
            type: "warning",
          }
        )
          .then(() => {
            this.$store.commit("conditions/getStypeIndex", index); //把获取的类型索引值保存
            this.rulesForm.conditions = [];
            this.rulesForm.asserts = [];
            this.$message({
              type: "success",
              message: this.$t("ruleCreate.switched"),
            });
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: this.$t("ruleCreate.cancelSwitch"),
            });
            this.getRuleData();
          });
      }
    },
    // 验证Conditions输入的参数
    checkConditionsData(data) {
      for (const item of data) {
        if (item.type === "group" && item.value.length == 0) {
          // alert("条件参数有条件组没有添加条件,请将条件设置完整!!");
          this.$alert(
            this.$t("ruleCreate.conditionGroupNoConditionTips"),
            this.$t("ruleCreate.tips")
          );
          this.checkConditionsValue = false;
          return;
        }
        if (
          item.type === "meta" &&
          (item.operator === "" || item.operatorType === -1)
        ) {
          // alert("条件参数有操作符未选择,请检查所有填写项是否均已填写!!");
          this.$alert(
            this.$t("ruleCreate.conditionNoOperatorTips"),
            this.$t("ruleCreate.tips")
          );
          this.checkConditionsValue = false;
          return;
        }
        if (
          item.type === "meta" &&
          item.operator === "range" &&
          (item.value[0] === "" ||
            item.value[0] === null ||
            item.value[1] === "" ||
            item.value[1] === null)
        ) {
          // alert("条件参数的值设定不全,请检查所有填写项是否均已填写!!");
          this.$alert(
            this.$t("ruleCreate.conditionNoValueTips"),
            this.$t("ruleCreate.tips")
          );
          this.checkConditionsValue = false;
          return;
        }
        // 新增：校验in操作符的数值类型输入
        if (
          item.type === "meta" &&
          item.operator === "in" &&
          item.operatorType === 0 &&
          item.value !== "" &&
          item.value !== null
        ) {
          // 检查逗号分隔的值是否都能转换为数字
          const values = item.value.split(',').map(v => v.trim());
          const hasInvalidNumber = values.some(v => v !== "" && isNaN(+v));
          if (hasInvalidNumber) {
            this.$alert(
              `条件参数表单项中,${item.field} 集合中操作符的数值类型应为数字，请检查输入内容是否为有效数值`,
              this.$t("ruleCreate.tips")
            );
            this.checkConditionsValue = false;
            return;
          }
        }
        if (
          item.type === "meta" &&
          (item.value === null || item.value === "")
        ) {
          // alert("条件参数的值设定不全,请检查所有填写项是否均已填写!!");
          this.$alert(
            this.$t("ruleCreate.conditionNoValueTips"),
            this.$t("ruleCreate.tips")
          );
          this.checkConditionsValue = false;
          return;
        }

        if (item.value && item.value.length > 0) {
          this.checkConditionsData(item.value);
        }
      }
    },
    // 验证asserts数据方法
    checkAssertsData(data) {
      for (const item of data) {
        if (item.type === "group" && item.value.length == 0) {
          // alert("目标参数有条件组没有添加条件,请将条件设置完整!!");
          this.$alert(
            this.$t("ruleCreate.assertsGroupNoConditionTips"),
            this.$t("ruleCreate.tips")
          );
          this.checkAssertsValue = false;
          return;
        }
        if (
          item.type === "meta" &&
          (item.operator === "" || item.operatorType === -1)
        ) {
          // alert("目标参数有操作符未选择,请检查所有填写项是否均已填写!!");
          this.$alert(
            this.$t("ruleCreate.assertsNoOperatorTips"),
            this.$t("ruleCreate.tips")
          );
          this.checkAssertsValue = false;
          return;
        }
        if (
          item.type === "meta" &&
          item.operator === "range" &&
          (item.value[0] === "" ||
            item.value[0] === null ||
            item.value[1] === "" ||
            item.value[1] === null)
        ) {
          // alert("目标参数的值设定不全,请检查所有填写项是否均已填写!!");
          this.$alert(
            this.$t("ruleCreate.assertsNoValueTips"),
            this.$t("ruleCreate.tips")
          );
          this.checkAssertsValue = false;
          return;
        }
        // 新增：校验in操作符的数值类型输入
        if (
          item.type === "meta" &&
          item.operator === "in" &&
          item.operatorType === 0 &&
          item.value !== "" &&
          item.value !== null
        ) {
          // 检查逗号分隔的值是否都能转换为数字
          const values = item.value.split(',').map(v => v.trim());
          const hasInvalidNumber = values.some(v => v !== "" && isNaN(+v));
          console.log('item',item);
          if (hasInvalidNumber) {
            this.$alert(
              `目标参数表单项中,${item.field} 集合中操作符的数值类型应为数字，请检查输入内容是否为有效数值`,
              this.$t("ruleCreate.tips")
            );
            this.checkAssertsValue = false;
            return;
          }
        }
        if (
          item.type === "meta" &&
          (item.value === null || item.value === "")
        ) {
          // alert("目标参数的值设定不全,请检查所有填写项是否均已填写!!");
          this.$alert(
            this.$t("ruleCreate.assertsNoValueTips"),
            this.$t("ruleCreate.tips")
          );
          this.checkAssertsValue = false;
          return;
        }

        if (item.value && item.value.length > 0) {
          this.checkAssertsData(item.value);
        }
      }
    },

    // 处理导出表单数据方法
    handleFormData(data) {
      for (const item of data) {
        if (item.type === "group" && item.operator === "and") {
          item.prefix = "true";
          item.operator = "and";
        }
        if (item.type === "group" && item.operator === "or") {
          item.prefix = "true";
          item.operator = "or";
        }
        if (item.type === "group" && item.operator === "not and") {
          item.prefix = "false";
          item.operator = "and";
        }
        if (item.type === "group" && item.operator === "not or") {
          item.prefix = "false";
          item.operator = "or";
        }
        // 处理 in 操作符：转换为对应类型的数组
        if (item.operator === "in") {
          // 修改前：简单的数据处理
          // if (item.operatorType === 0) item.value = [+item.value];
          // if (item.operatorType === 1) item.value = item.value.split();
          
          // 修改后：支持逗号分隔的多个值
          if (item.operatorType === 0) {
            // 数值类型：按逗号分割，转换为数值数组
            item.value = item.value.split(',').map(v => +v.trim());
          } else if (item.operatorType === 1) {
            // 字符串类型：按逗号分割，去除空格
            item.value = item.value.split(',').map(v => v.trim());
          }
        }
        
        // 处理 range 操作符：转换为对应类型的数组
        if (item.operator === "range") {
          if (item.operatorType === 0) {
            // 数值类型：转换数组元素为数值
            item.value = [+item.value[0], +item.value[1]];
          } else if (item.operatorType === 1) {
            // 字符串类型：保持字符串数组
            item.value = [item.value[0].toString(), item.value[1].toString()];
          }
        }
        
        // 处理其他操作符：根据 operatorType 转换单个值的类型
        if (item.operator !== "in" && item.operator !== "range") {
          if (item.operatorType === 0) {
            // 数值类型：转换为数值
            item.value = +item.value;
          } else if (item.operatorType === 1) {
            // 字符串类型：确保为字符串
            item.value = item.value.toString();
          } else if (item.operatorType === 2) {
            // 布尔类型：确保为布尔值（通常已经是正确类型）
            item.value = Boolean(item.value);
          }
        }

        if (item.value && item.value.length > 0) {
          this.handleFormData(item.value);
        }
      }
    },

    // 处理表单规则数据传参
    processRuleData() {
      let formData = "";
      // 定义一个常量保存表单数据
      const rulesData = JSON.parse(JSON.stringify(this.rulesForm));
      // 没有任何条件参数点击提交的弹窗提示
      if (rulesData.asserts && rulesData.asserts.length == 0) {
        // alert("请在目标参数至少添加一个条件组!!");
        this.$alert(
          this.$t("ruleCreate.targetParameterAddConditionTips"),
          this.$t("ruleCreate.tips")
        );
        return formData;
      }
      // 调用验证表单数据的方法
      this.checkAssertsData(rulesData.asserts);
      // 验证值为false则退出提交事件 并且把赋值还原为true
      if (!this.checkAssertsValue) {
        this.checkAssertsValue = true;
        return formData;
      }

      if (rulesData.conditions && rulesData.conditions.length > 0) {
        this.checkConditionsData(rulesData.conditions);
      }
      if (!this.checkConditionsValue) {
        this.checkConditionsValue = true;
        return formData;
      }
      // 调用处理表单数据的方法
      this.handleFormData(rulesData.asserts);
      this.handleFormData(rulesData.conditions);
      // 处理需要导出JSON的数据
      const { stype, ruleLevel, name, isEnabled } = this.form;
      const { conditions, asserts } = rulesData;
      const condition = conditions[0] ? conditions[0] : {};
      const assert = asserts[0] ? asserts[0] : {};
      formData = { stype, ruleLevel, isEnabled, name, condition, assert };
      return formData;
    },
    // 测试规则事件
    async testRule() {
      let formData = this.processRuleData();
      if (!formData) return;
      let { code, data, msg } = await testRule({
        projectId: this.form.projectId,
        projectBranch: this.form.projectBranch,
        ruleForm: JSON.stringify(formData),
      });
      if (code === 200) {
        this.testRuleData = data;
        this.testRuleVisible = true;
      } else {
        this.$message.error(msg);
      }
      // this.testRuleVisible = true;
    },

    // 关闭测试规则数据弹窗
    closeTestRule(val) {
      this.testRuleVisible = val;
    },

    // 下载规则JSON文件
    onDownload() {
      let formData = this.processRuleData();
      if (!formData) return;
      // 导出JSON文件;
      const blob = new Blob([JSON.stringify(formData, null, 2)], {
        type: "application/json",
      });
      const name = formData.name;
      // 保存json文件 文件名称使用规则名称
      this.$confirm(
        this.$t("ruleCreate.isExportJsonFile"),
        this.$t("ruleCreate.tips"),
        {
          type: "warning",
        }
      )
        .then(async () => {
          // 保存json文件 文件名称使用规则名称
          FileSaver.saveAs(blob, name);
          this.$message.success(this.$t("ruleCreate.downloadStarted"));
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: this.$t("ruleCreate.exportCanceled"),
          });
        });
    },

    // 保存修改规则数据
    onSubmit() {
      this.$refs.form.validate((valid) => {
        if (!valid) {
          this.$message({
            type: "warning",
            message: this.$t("ruleCreate.requiredNotEmptyTips"),
          });
          return;
        } else {
          let formData = this.processRuleData();
          if (!formData) return;
          // 保存json文件 文件名称使用规则名称
          this.$confirm(
            this.$t("ruleCreate.isSaveRule"),
            this.$t("ruleCreate.tips"),
            {
              type: "warning",
            }
          )
            .then(async () => {
              // 保存规则请求方法
              let params = {
                ruleId: this.$route.query.ruleId,
                ruleForm: JSON.stringify(formData),
              };
              // 发送修改请求
              let { code, msg } = await updateRule(params);
              if (code === 200) {
                this.$message({
                  type: "success",
                  message: this.$t("ruleCreate.saved"),
                });
              }
            })
            .catch(() => {
              this.$message({
                type: "info",
                message: this.$t("ruleCreate.saveCanceled"),
              });
            });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.rule-form {
  background-color: #fff;
  padding: 15px;
  padding-top: 25px;
}
</style>
