import request from '@/utils/request'
import Qs from 'qs'

// mock后台拿到的所有权限路由数据
// Just a mock data
// const asyncRoutes = [
//   {
//     path: '/',
//     children: [
//       {
//         path: 'home',
//         meta: { title: '首页' }
//       }
//     ]
//   },
//   {
//     path: '/project',
//     children: [
//       {
//         path: 'index',
//         meta: { title: '我的项目' }
//       }
//     ]
//   },
//   {
//     path: '/prodetail',
//     children: [
//       {
//         path: 'index',
//         meta: { title: '项目报告' }
//       }
//     ]
//   },
//   {
//     path: '/proreport',
//     children: [
//       {
//         path: 'detail',
//         meta: { title: '报告详情' }
//       },
//       {
//         path: 'comparison',
//         meta: { title: '报告对比' }
//       }
//     ]
//   },

//   {
//     path: '/rulelist',
//     children: [
//       {
//         path: 'index',
//         meta: { title: '规则列表' }
//       }
//     ]
//   },
//   {
//     path: '/ruleedit',
//     children: [
//       {
//         path: 'checkrule',
//         meta: { title: '规则编辑' }
//       }
//     ]
//   },

//   {
//     path: '/rulecreate',
//     children: [
//       {
//         path: 'index',
//         meta: { title: '创建规则' }
//       }
//     ]
//   },
//   {
//     path: '/permission',
//     meta: {
//       title: '权限配置'
//     },
//     children: [
//       {
//         path: 'role',
//         meta: {
//           title: '角色管理'
//         }
//       },
//       {
//         path: 'project',
//         meta: {
//           title: '项目管理'
//         }
//       },
//       {
//         path: 'users',
//         meta: {
//           title: '用户管理'
//         }
//       }
//     ]
//   },
//   {
//     path: '/helpguide',
//     meta: {
//       title: '帮助指引'
//     },
//     children: [
//       {
//         path: 'guide1',
//         meta: {
//           title: '帮助指引01'
//         }
//       },
//       {
//         path: 'guide2',
//         meta: {
//           title: '帮助指引02'
//         }
//       }
//     ]
//   },
//   { path: '*', redirect: '/404', hidden: true }
// ]

// export function getRoutes() {
//   // return request({
//   //   url: '/vue-element-admin/routes',
//   //   method: 'get'
//   // })
//   return asyncRoutes
// }

export function getRoles(query) {
  return request({
    url: '/get_roles',
    method: 'get',
    params: query
  })
}

export function addRole(data) {
  data = Qs.stringify(data)
  return request({
    url: '/create_role',
    method: 'post',
    data
  })
}

export function updateRole(data) {
  data = Qs.stringify(data)
  return request({
    url: `/edit_role`,
    method: 'post',
    data
  })
}

export function deleteRole(data) {
  data = Qs.stringify(data)
  return request({
    url: `/delete_role`,
    method: 'post',
    data,
  })
}
