<template>
  <div class="app-container">
    <div class="rule-form">
      <div class="el-page-header">
        <div class="el-page-header__content">
          {{ $t("ruleCreate.ruleCreate") }}
        </div>
      </div>
      <el-divider />
      <el-form ref="form" :model="form" :rules="rules" label-width="95px">
        <el-form-item :label="$t('ruleCreate.projectBranch')" prop="projectId">
          <div
            style="
              display: flex;
              justify-content: space-between;
              align-items: center;
            "
          >
            <div>
              <el-select
                v-model="form.projectId"
                :placeholder="$t('ruleCreate.selectProject')"
                style="width: 200px; margin-right: 10px"
                @change="changeProject"
              >
                <el-option
                  v-for="(item, index) in userProjectList"
                  :key="index"
                  :label="item.projectName"
                  :value="item.projectId"
                />
              </el-select>
              <el-select
                v-model="form.projectBranch"
                :placeholder="$t('ruleCreate.selectBranch')"
                style="width: 150px"
                @change="changeBranch"
              >
                <el-option
                  v-for="(item, index) in projectBranchData"
                  :key="index"
                  :label="item.branch"
                  :value="item.branch"
                />
              </el-select>
            </div>

            <el-radio-group
              v-model="form.isEnabled"
              :fill="form.isEnabled ? '#13ce66' : '#F56C6C'"
            >
              <el-radio-button :label="true">{{
                form.isEnabled
                  ? $t("ruleCreate.enabled")
                  : $t("ruleCreate.enable")
              }}</el-radio-button>
              <el-radio-button :label="false">{{
                form.isEnabled
                  ? $t("ruleCreate.ignore")
                  : $t("ruleCreate.ignored")
              }}</el-radio-button>
            </el-radio-group>
          </div>
        </el-form-item>
        <el-form-item :label="$t('ruleCreate.ruleName')" prop="name">
          <el-input
            v-model="form.name"
            :placeholder="$t('ruleCreate.enterName')"
            :clearable="true"
            style="width: 250px; margin-right: 10px"
          />
          <el-select
            v-model="form.ruleLevel"
            name="ruleLevel"
            :placeholder="'选取优先级'"
            style="width: 120px"
          >
            <el-option label="重要" value="important"></el-option>
            <el-option label="一般" value="normal"></el-option>
            <el-option label="忽略" value="ignore"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="表头选择" :rules="{ required: true }">
          <div
            v-if="form.tableHeaders.length > 0"
            style="
              padding: 0 5px;
              background-color: #edeff2;
              border: 1px solid #999;
              border-radius: 4px;
            "
          >
            <div v-for="(item, index) in form.tableHeaders" :key="index">
              <span>{{ "Col_" + index + "：" }}</span>
              <el-select
                v-model="item.tableName"
                name="tableName"
                :placeholder="'请选择表格'"
                filterable
                @change="changeTable($event, index)"
                size="mini"
                style="width: 300px"
              >
                <el-option
                  v-for="o in tableList"
                  :key="o"
                  :label="o"
                  :value="o"
                />
              </el-select>
              <el-select
                v-if="item.tableName"
                v-model="item.sheet"
                name="sheet"
                :placeholder="'请选择表单'"
                @change="changeSheet(index)"
                size="mini"
                style="margin-left: 5px"
              >
                <el-option
                  v-for="(value, key) in sheetList[item.tableName]"
                  :key="key"
                  :label="key"
                  :value="key"
                />
              </el-select>
              <el-select
                v-if="item.sheet"
                v-model="item.header"
                name="header"
                :placeholder="'请选择表头'"
                size="mini"
                style="margin-left: 5px"
              >
                <el-option
                  v-for="(o, i) in sheetList[item.tableName][item.sheet]"
                  :key="i"
                  :label="o"
                  :value="o"
                />
              </el-select>
              <el-button
                type="info"
                circle
                icon="el-icon-minus"
                size="mini"
                style="margin-left: 10px"
                @click="removeTableHeader(index)"
              ></el-button>
            </div>
            <el-button
              icon="el-icon-plus"
              type="info"
              size="mini"
              @click="addTableHeader()"
              >添加表头</el-button
            >
            <el-button
              icon="el-icon-remove"
              type="info"
              size="mini"
              style="margin-left: 5px"
              @click="removeAllTableHeaders()"
              >清空表头</el-button
            >
          </div>
          <el-button
            v-if="form.tableHeaders.length === 0"
            icon="el-icon-plus"
            type="primary"
            size="mini"
            style="margin-left: 0"
            @click="addTableHeader()"
            >{{ "添加表头" }}</el-button
          >
        </el-form-item>
        <!-- 条件参数子组件 -->
        <el-form-item :label="$t('ruleCreate.conditionParameter')">
          <div>
            <el-button
              v-if="tableRulesForm.conditions.length === 0"
              icon="el-icon-plus"
              type="primary"
              size="mini"
              style="margin-left: 0"
              @click="addFirstConditionGroup('condition')"
              >{{ $t("ruleCreate.addConditionGroup") }}</el-button
            >
            <Conditions
              v-else
              :conditions="tableRulesForm.conditions"
              :tableHeaders="form.tableHeaders"
              :operatorList="operatorList"
              :belongType="'条件参数'"
            />
          </div>
        </el-form-item>
        <el-form-item label="运算参数" prop="calc">
          <div
            v-if="form.calc.length > 0"
            style="
              padding: 0 5px;
              background-color: #edeff2;
              border: 1px solid #999;
              border-radius: 4px;
            "
          >
            <div v-for="(item, index) in form.calc" :key="index">
              <el-select
                v-model="item.tableHeader"
                name="tableHeader"
                :placeholder="'请选择表头'"
                filterable
                size="mini"
              >
                <el-option
                  v-for="(o, i) in form.tableHeaders"
                  :key="i"
                  :label="'Col_' + i"
                  :value="'Col_' + i"
                />
              </el-select>
              <el-select
                v-model="item.operator"
                name="operator"
                placeholder="请选择运算符"
                size="mini"
                style="margin-right: 5px"
              >
                <div v-for="o in operatorList">
                  <el-option
                    v-if="o.belong_type.includes('运算参数')"
                    :key="o.id"
                    :label="o.name_cn"
                    :value="o.name_cn"
                  />
                </div>
              </el-select>
              <el-autocomplete
                v-if="isShowInput(item.operator)"
                v-model="item.value"
                :fetch-suggestions="querySearch"
                size="mini"
                clearable
                placeholder="请选择或者填写目标值"
              ></el-autocomplete>
              <el-button
                type="info"
                circle
                icon="el-icon-minus"
                size="mini"
                style="margin-left: 10px"
                @click="removeCalc(index)"
              ></el-button>
            </div>
            <el-button
              icon="el-icon-plus"
              type="info"
              size="mini"
              @click="addCalc()"
              >添加运算参数</el-button
            >
            <el-button
              icon="el-icon-remove"
              type="info"
              size="mini"
              style="margin-left: 5px"
              @click="removeAllCalc()"
              >删除所有参数</el-button
            >
          </div>
          <el-button
            v-if="form.calc.length === 0"
            icon="el-icon-plus"
            type="primary"
            size="mini"
            style="margin-left: 0"
            @click="addCalc()"
            >{{ "添加运算参数" }}</el-button
          >
        </el-form-item>
        <el-form-item label="合表参数" prop="mergeParams">
          <div
            v-if="form.mergeParams.length > 0"
            style="
              padding: 0 5px;
              background-color: #edeff2;
              border: 1px solid #999;
              border-radius: 4px;
            "
          >
            <div v-for="(item, index) in form.mergeParams" :key="index">
              <el-select
                v-model="item.firstTable"
                name="firstTable"
                :placeholder="'请选择表格'"
                filterable
                size="mini"
              >
                <el-option
                  v-for="(o, i) in form.tableHeaders"
                  :key="i"
                  :label="'Col_' + i"
                  :value="'Col_' + i"
                />
              </el-select>
              <el-select
                v-model="item.merge"
                name="merge"
                :placeholder="'请选择选项'"
                size="mini"
                style="margin-left: 5px"
              >
                <el-option label="合表" value="merge" />
              </el-select>
              <el-select
                v-model="item.secondTable"
                name="secondTable"
                :placeholder="'请选择表格'"
                size="mini"
                style="margin-left: 5px"
              >
                <el-option
                  v-for="(o, i) in form.tableHeaders"
                  :key="i"
                  :label="'Col_' + i"
                  :value="'Col_' + i"
                />
              </el-select>
              <el-button
                type="info"
                circle
                icon="el-icon-minus"
                size="mini"
                style="margin-left: 10px"
                @click="removeMergeParams(index)"
              ></el-button>
            </div>
            <el-button
              icon="el-icon-plus"
              type="info"
              size="mini"
              @click="addMergeParams()"
              >添加合表参数</el-button
            >
            <el-button
              icon="el-icon-remove"
              type="info"
              size="mini"
              style="margin-left: 5px"
              @click="removeAllMergeParams()"
              >删除所有参数</el-button
            >
          </div>
          <el-button
            v-if="form.mergeParams.length === 0"
            icon="el-icon-plus"
            type="primary"
            size="mini"
            style="margin-left: 0"
            @click="addMergeParams()"
            >{{ "添加合表参数" }}</el-button
          >
        </el-form-item>
        <!-- 目标参数 -->
        <el-form-item
          :label="$t('ruleCreate.targetParameter')"
          :rules="{ required: true }"
        >
          <div>
            <el-button
              v-if="tableRulesForm.asserts.length === 0"
              icon="el-icon-plus"
              type="primary"
              size="mini"
              style="margin-left: 0"
              @click="addFirstConditionGroup('assert')"
              >{{ $t("ruleCreate.addConditionGroup") }}</el-button
            >
            <Conditions
              v-else
              :conditions="tableRulesForm.asserts"
              :tableHeaders="form.tableHeaders"
              :operatorList="operatorList"
              :belongType="'目标参数'"
            />
          </div>
        </el-form-item>
        <el-form-item label="错误描述">
          <el-input
            v-model="form.description"
            placeholder="错误描述(尽量简短易懂)"
          ></el-input>
        </el-form-item>
        <el-form-item label="结果输出">
          <div style="display: flex; justify-content: space-between">
            <el-input
              type="textarea"
              :disabled="true"
              :rows="10"
              placeholder="测试结果输出框"
              v-model="testRuleData"
              class="test-result"
              style="margin-right: 15px"
            >
            </el-input>
            <div style="display: flex; flex-direction: column">
              <el-button
                style="margin-bottom: 15px"
                type="info"
                @click="testRule()"
                >{{ $t("ruleCreate.testRule")
                }}<i class="el-icon-document-checked el-icon--right"></i
              ></el-button>
              <!-- 引入规则导入子组件 -->
              <div style="margin-bottom: 15px">
                <el-button type="primary" @click="importRule"
                  >{{ $t("ruleCreate.importRule")
                  }}<i class="el-icon-upload el-icon--right"></i
                ></el-button>
              </div>
              <ImportData ref="importRule" />

              <div>
                <el-button type="primary" @click="onDownload"
                  >{{ $t("ruleCreate.downloadRule")
                  }}<i class="el-icon-download el-icon--right"></i
                ></el-button>
              </div>
              <el-button
                style="margin-top: 15px"
                type="success"
                @click="onSubmit"
                >{{ $t("ruleCreate.saveRule")
                }}<i class="el-icon-circle-check el-icon--right"></i
              ></el-button>
            </div>
          </div>
        </el-form-item>
        <!-- <el-form-item size="large">
          <div style="display: flex; margin-top: 15px">
            <el-button type="info" @click="testRule()"
              >{{ $t("ruleCreate.testRule")
              }}<i class="el-icon-document-checked el-icon--right"></i
            ></el-button>

            <el-button
              style="margin-right: 10px"
              type="primary"
              @click="importRule"
              >{{ $t("ruleCreate.importRule")
              }}<i class="el-icon-upload el-icon--right"></i
            ></el-button>
            <ImportData ref="importRule" />

            <el-button type="primary" @click="onDownload"
              >{{ $t("ruleCreate.downloadRule")
              }}<i class="el-icon-download el-icon--right"></i
            ></el-button>
            <el-button
              type="success"
              @click="onSubmit"
              style="margin-left: 10px"
              >{{ $t("ruleCreate.saveRule")
              }}<i class="el-icon-circle-check el-icon--right"></i
            ></el-button>
          </div>
        </el-form-item> -->
      </el-form>
    </div>
  </div>
</template>

<script>
import FileSaver from "file-saver"; // 引入导出JSON文件的插件
import Conditions from "./components/Conditions.vue";
import ImportData from "./components/ImportData.vue";
import { mapGetters } from "vuex";
import {
  getExcelList,
  getExcelSheetList,
  getExcelOperator,
  createExcelRule,
  testExcelRule,
} from "@/api/rule";
import { getProjectBranchs, getUserProjectList } from "@/api/project";

export default {
  name: "TableExamine",
  components: { Conditions, ImportData },
  data() {
    return {
      userProjectList: [],
      projectBranchData: [],
      excelList: [],

      key: Date.now(), //条件绑定的key
      checkConditionsValue: true, // 保存验证表单条件数据是否通过
      checkAssertsValue: true, // 保存验证表单条件数据是否通过

      tableList: [], //表格选择列表
      tableOption: [],
      sheetList: {},
      operatorList: [], //保存运算符
      // 保存所有表单的数据
      form: {
        projectId: void 0,
        projectBranch: "",
        isEnabled: true,
        name: "",
        ruleLevel: "normal",
        // stype: "",
        tableHeaders: [],
        calc: [],
        mergeParams: [],
        description: "",
      },
      oruginalProjectValue: "",
      originalBranchValue: "",

      rules: {
        //规则类型和名称的验证规则
        projectId: [
          {
            required: true,
            message: this.$t("ruleCreate.projectBranchRequired"),
            trigger: "change",
          },
        ],
        name: [
          {
            required: true,
            message: this.$t("ruleCreate.ruleNameRequired"),
            trigger: "blur",
          },
          {
            pattern: /^[^\?\:\*\"\<\>\|\/\\\？\：\“\”]*$/,
            message: '名称不能包含\\ / : * ? " < > |',
            trigger: "change",
          },
        ],
      },

      testRuleData: "", //保存测试规则信息
    };
  },
  computed: {
    ...mapGetters(["tableRulesForm"]),
  },

  mounted() {
    this.getUserProjectList();
    this.getOperatorList();
    this.form.name = this.tableRulesForm.name;
    this.form.ruleLevel = this.tableRulesForm.ruleLevel;
    this.form.isEnabled = this.tableRulesForm.isEnabled;
    this.form.tableHeaders = this.tableRulesForm.tableHeaders;
    this.form.calc = this.tableRulesForm.calc;
    this.form.mergeParams = this.tableRulesForm.mergeParams;
    this.form.description = this.tableRulesForm.description;
    if (localStorage.getItem("rule_table_sheet")) {
      this.sheetList = JSON.parse(localStorage.getItem("rule_table_sheet"));
      localStorage.removeItem("rule_table_sheet");
    }
  },
  // created() {
  //   //在页面刷新时将form数据保存到localStorage里
  //   window.addEventListener("beforeunload", () => {
  //     localStorage.setItem("rule_table_sheet", JSON.stringify(this.sheetList)); //离开页面保存sheetlist
  //     const formData = {
  //       projectId: this.form.projectId,
  //       projectBranch: this.form.projectBranch,
  //       name: this.form.name,
  //       ruleLevel: this.form.ruleLevel,
  //       isEnabled: this.form.isEnabled,
  //       tableHeaders: this.form.tableHeaders,
  //       conditions: this.tableRulesForm.conditions,
  //       calc: this.form.calc,
  //       mergeParams: this.form.mergeParams,
  //       asserts: this.tableRulesForm.asserts,
  //       description: this.form.description,
  //     };
  //     localStorage.setItem("rule_table_form", JSON.stringify(formData));
  //   });
  // },
  //在页面离开时
  beforeRouteLeave(to, from, next) {
    // localStorage.setItem("rule_table_sheet", JSON.stringify(this.sheetList)); //离开页面保存sheetlist
    // const formData = {
    //   projectId: this.form.projectId,
    //   projectBranch: this.form.projectBranch,
    //   name: this.form.name,
    //   ruleLevel: this.form.ruleLevel,
    //   isEnabled: this.form.isEnabled,
    //   tableHeaders: this.form.tableHeaders,
    //   conditions: this.tableRulesForm.conditions,
    //   calc: this.form.calc,
    //   mergeParams: this.form.mergeParams,
    //   asserts: this.tableRulesForm.asserts,
    //   description: this.form.description,
    // };
    // localStorage.setItem("rule_table_form", JSON.stringify(formData));
    // 清空form数据
    this.clearFormData();
    next();
  },

  watch: {
    // 监控表单数据变化
    "tableRulesForm.name"(newValue, oldValue) {
      this.form.name = newValue;
    },
    "tableRulesForm.ruleLevel"(newValue, oldValue) {
      this.form.ruleLevel = newValue;
    },
    "tableRulesForm.isEnabled"(newValue, oldValue) {
      this.form.isEnabled = newValue;
    },
    "tableRulesForm.tableHeaders"(newValue, oldValue) {
      this.form.tableHeaders = newValue;
    },
    "tableRulesForm.calc"(newValue, oldValue) {
      this.form.calc = newValue;
    },
    "tableRulesForm.mergeParams"(newValue, oldValue) {
      this.form.mergeParams = newValue;
    },
    "tableRulesForm.description"(newValue, oldValue) {
      this.form.description = newValue;
    },
    form: {
      handler(newVal) {
        this.$store.commit("tableCondition/setTableFormData", newVal);
      },
      deep: true,
    },
  },

  methods: {
    // 清空所有表单数据
    clearFormData() {
      let ruleData = {
        isEnabled: true,
        name: "",
        ruleLevel: "normal",
        tableHeaders: [],
        condition: {},
        calc: [],
        mergeParams: [],
        assert: {},
        description: "",
      };
      this.$store.commit("tableCondition/setUploadData", ruleData);
      this.testRuleData = "";
    },
    // 添加表头
    addTableHeader() {
      this.form.tableHeaders.push({
        tableName: "",
        sheet: "",
        header: "",
      });
    },
    // 删除表头
    removeTableHeader(index) {
      this.form.tableHeaders.splice(index, 1);
    },
    // 删除所有表头
    removeAllTableHeaders() {
      this.$confirm("将清空所有表头，是否继续？", this.$t("ruleCreate.tips"), {
        type: "warning",
      })
        .then(() => {
          this.$message({
            type: "success",
            message: this.$t("ruleCreate.deleted"),
          });
          this.form.tableHeaders = [];
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: this.$t("ruleCreate.cancelDelete"),
          });
        });
    },
    // 添加运算参数
    addCalc() {
      if (this.form.tableHeaders.length === 0)
        return this.$message.warning("请先添加表头");
      this.form.calc.push({
        tableHeader: "",
        operator: "",
        value: "",
      });
    },
    removeCalc(index) {
      this.form.calc.splice(index, 1);
    },
    removeAllCalc() {
      this.$confirm(
        "将删除所有运算参数，是否继续？",
        this.$t("ruleCreate.tips"),
        {
          type: "warning",
        }
      )
        .then(() => {
          this.$message({
            type: "success",
            message: this.$t("ruleCreate.deleted"),
          });
          this.form.calc = [];
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: this.$t("ruleCreate.cancelDelete"),
          });
        });
    },
    // 获取运算符
    async getOperatorList() {
      const { code, data } = await getExcelOperator();
      if (code !== 200) return this.$message.error("获取运算符失败");
      this.operatorList = data;
    },
    // 切换运算符是否展示对应输入框
    isShowInput(val) {
      if (!val) return true;
      const operatorIndex = this.operatorList.findIndex(
        (obj) => obj["name_cn"] === val
      );
      const expected_type = this.operatorList[operatorIndex].expected_type;
      if (expected_type !== null) return true;
      return false;
    },
    // 运算参数的目标值表头选择项
    querySearch(queryString, cb) {
      let tableList = this.form.tableHeaders.map((item, index) => {
        return {
          value: "Col_" + index,
        };
      });
      let results = queryString
        ? tableList.filter(this.createFilter(queryString))
        : tableList;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    createFilter(queryString) {
      return (table) => {
        return (
          table.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1
        );
      };
    },
    // 验证运算参数数据
    checkCalc(data) {
      for (let index = 0; index < data.length; index++) {
        const item = data[index];
        if (
          item.tableHeader === "" ||
          item.operator === "" ||
          (this.isShowInput(item.operator) && item.value === "")
        ) {
          this.$message({
            type: "warning",
            message: "运算参数未填写完成，请检查所有填写项是否均已填写！",
          });
          return false;
        }
      }
      return true;
    },

    // 添加合表参数
    addMergeParams() {
      if (this.form.tableHeaders.length < 2)
        return this.$message.warning("请先添加表头且大于2个");
      this.form.mergeParams.push({
        firstTable: "",
        merge: "merge",
        secondTable: "",
      });
    },
    // 删除合表参数
    removeMergeParams(index) {
      this.form.mergeParams.splice(index, 1);
    },
    // 删除所有合表参数
    removeAllMergeParams() {
      this.$confirm(
        "将删除所有合表参数，是否继续？",
        this.$t("ruleCreate.tips"),
        {
          type: "warning",
        }
      )
        .then(() => {
          this.$message({
            type: "success",
            message: this.$t("ruleCreate.deleted"),
          });
          this.form.mergeParams = [];
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: this.$t("ruleCreate.cancelDelete"),
          });
        });
    },
    // 验证合表参数数据
    checkMergeParams() {
      for (let index = 0; index < this.form.mergeParams.length; index++) {
        const item = this.form.mergeParams[index];
        const index1 = item.firstTable ? item.firstTable.split("_").at(-1) : "";
        const index2 = item.secondTable
          ? item.secondTable.split("_").at(-1)
          : "";
        if (!index1 || !index2) {
          this.$message({
            type: "warning",
            message: "合表参数未填写完成,请检查所有填写项是否均已填写！",
          });
          return false;
        }
        if (!this.form.tableHeaders[index1]) {
          this.$message({
            type: "warning",
            message: "Col_" + index1 + " 表头不存在,请修改所在填写项！",
          });
          return false;
        }
        if (!this.form.tableHeaders[index2]) {
          this.$message({
            type: "warning",
            message: "Col_" + index2 + " 表头不存在,请修改所在填写项！",
          });
          return false;
        }
      }
      return true;
    },

    // 导入规则
    importRule() {
      this.$refs.importRule.importData(this.form);
    },
    // 获取所有项目
    async getUserProjectList() {
      const params = { isExcel: true, pageNum: 1, pageSize: 99999 };
      let { code, projectList, dataLen } = await getUserProjectList(params);
      if (code === 200) {
        this.userProjectList = projectList;
        // if (localStorage.getItem("rule_table_form")) {
        //   let formData = JSON.parse(localStorage.getItem("rule_table_form"));
        //   // 判断如果项目已被删除,则清除数据
        //   let findId = projectList.some((item) => {
        //     return item.projectId === formData.projectId;
        //   });
        //   if (findId) {
        //     this.form.projectId = formData.projectId;
        //     this.oruginalProjectValue = formData.projectId;
        //     this.form.projectBranch = formData.projectBranch;
        //     this.getProjectBranchs(formData.projectId);
        //     this.$store.commit("tableCondition/setTableFormData", formData);
        //     localStorage.removeItem("rule_table_form");
        //   } else {
        //     // this.form.isEnabled = true;
        //     this.$store.commit("tableCondition/setTableFormData", formData);
        //     localStorage.removeItem("rule_table_form");
        //   }
        // }
      }
    },
    // 获取项目所有分支
    async getProjectBranchs(projectId) {
      let { code, data } = await getProjectBranchs({ projectId });
      if (code === 200) {
        this.projectBranchData = data;
        if (!this.form.projectBranch && data.length > 0) {
          this.form.projectBranch = this.projectBranchData[0].branch;
          this.originalBranchValue = this.projectBranchData[0].branch;
        }
        // 获取所对应表格名
        this.getExcelList();
      }
    },
    // 获取表格名list
    async getExcelList() {
      if (!this.form.projectId && !this.form.projectBranch) return;

      const { code, data, msg } = await getExcelList({
        projectId: this.form.projectId,
        projectBranch: this.form.projectBranch,
      });
      if (code !== 200) return this.$message.warning(msg);
      this.tableList = data;
      data.forEach((item) => {
        this.getTableSheet(item);
      });
      // for (let index = 0; index < data.length; index++) {
      //   const item = data[index];
      //   // 发起获取sheetlist请求
      //   // this.getTableSheet(item);
      //   const res = await getExcelSheetList({
      //     projectId: this.form.projectId,
      //     branch: this.form.projectBranch,
      //     table_name: item,
      //   });
      //   if (res.code !== 200) return this.$message.warning(res.msg);
      //   this.sheetList[item] = res.data;
      //   // console.log(this.sheetList);
      // }
    },
    // 切换表格时候清空表头数据
    changeTable(value, index) {
      this.form.tableHeaders[index].sheet = "";
      this.form.tableHeaders[index].header = "";
    },
    // 获取表格对应sheet数据
    async getTableSheet(tableName) {
      const { code, data, msg } = await getExcelSheetList({
        projectId: this.form.projectId,
        branch: this.form.projectBranch,
        table_name: tableName,
      });
      if (code !== 200) return this.$message.warning(msg);
      this.sheetList[tableName] = data;
    },
    changeSheet(index) {
      this.form.tableHeaders[index].header = "";
    },
    // 获取表格表头数据
    getTableHeader(sheetName) {
      // mock data
      const data1 = ["表头1", "表头2", "表头3"];
      const data2 = ["表头4", "表头5"];
      if (sheetName === "sheet1") return data1;
      return data2;
    },
    // 切换项目重新获取分支
    changeProject(value) {
      if (this.oruginalProjectValue === "") {
        this.oruginalProjectValue = value;
        this.getProjectBranchs(this.form.projectId);
        return;
      }
      if (this.form.tableHeaders.length === 0) {
        this.form.projectBranch = "";
        this.getProjectBranchs(this.form.projectId);
        return;
      }
      this.$confirm(
        "切换项目将清空填写数据，是否继续？",
        this.$t("ruleCreate.tips"),
        {
          type: "warning",
        }
      )
        .then(() => {
          this.originalBranchValue = "";
          this.form.projectBranch = "";
          this.getProjectBranchs(this.form.projectId);
          this.oruginalProjectValue = this.form.projectId;
          this.clearFormData();
        })
        .catch(() => {
          this.form.projectId = this.oruginalProjectValue;
        });
    },
    // 切换分支重新获取表格名
    changeBranch(value) {
      if (this.originalBranchValue === "")
        return (this.originalBranchValue = value);
      if (this.form.tableHeaders.length === 0) return this.getExcelList();
      this.$confirm(
        "切换分支将清空填写数据，是否继续？",
        this.$t("ruleCreate.tips"),
        {
          type: "warning",
        }
      )
        .then(() => {
          this.originalBranchValue = this.form.projectBranch;
          this.clearFormData();
          this.getExcelList();
        })
        .catch(() => {
          this.form.projectBranch = this.originalBranchValue;
        });
    },

    // 返回首页
    goHome() {
      this.$router.push("/");
    },

    // 当没有数据时先创建初始条件组的自定义事件
    addFirstConditionGroup(createType) {
      this.$store.commit("tableCondition/addFirstConditionGroup", createType);
    },
    // 获取对应类型所在的索引值 切换类型清空数据
    getStypeIndex(stype, index) {
      let oldStyle = JSON.parse(JSON.stringify(this.form.stype));
      // console.log(stype, this.tableRulesForm.stype);
      if (
        !this.form.stype ||
        (this.tableRulesForm.asserts.length === 0 &&
          this.tableRulesForm.conditions.length === 0) ||
        oldStyle === stype
      ) {
        this.$store.commit("tableCondition/setStypeIndex", index); //把获取的类型索引值保存
        return;
      }

      if (oldStyle !== stype) {
        this.$confirm(
          this.$t("ruleCreate.switchTypeTips"),
          this.$t("ruleCreate.tips"),
          {
            type: "warning",
          }
        )
          .then(() => {
            this.$store.commit("tableCondition/setStypeIndex", index); //把获取的类型索引值保存
            this.$message({
              type: "success",
              message: this.$t("ruleCreate.switched"),
            });
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: this.$t("ruleCreate.cancelSwitch"),
            });
            this.form.stype = oldStyle;
          });
      }
    },
    // 验证输入的条件参数
    checkConditionsData(data) {
      for (const item of data) {
        if (item.type === "group" && item.value.length == 0) {
          // alert("条件参数有条件组没有添加条件,请将条件设置完整!!");
          this.$message({
            type: "warning",
            message: this.$t("ruleCreate.conditionGroupNoConditionTips"),
          });
          this.checkConditionsValue = false;
          return;
        }
        if (item.type === "meta") {
          const index1 = item.tableHeader.split("_").at(-1);
          const index2 = item.value.includes("Col_")
            ? item.value.split("_").at(-1)
            : "notCol";
          if (index1 && !this.form.tableHeaders[index1]) {
            this.$message({
              type: "warning",
              message: "Col_" + index1 + " 表头不存在,请修改填写项！",
            });
            this.checkConditionsValue = false;
            return;
          }
          if (index2 !== "notCol" && !this.form.tableHeaders[index2]) {
            this.$message({
              type: "warning",
              message: "Col_" + index2 + " 表头不存在,请修改所在填写项！",
            });
            this.checkConditionsValue = false;
            return;
          }
        }
        if (
          item.type === "meta" &&
          (item.tableHeader === "" ||
            item.operator === "" ||
            (this.isShowInput(item.operator) && item.value === ""))
        ) {
          this.$message({
            type: "warning",
            message: "条件参数条件未填写完成，请检查所有填写项是否均已填写！",
          });
          this.checkConditionsValue = false;
          return;
        }
        this.checkConditionsValue = true;

        if (item.value && item.value.length > 0) {
          this.checkConditionsData(item.value);
        }
      }
    },
    // 验证目标参数asserts数据方法
    checkAssertsData(data) {
      for (const item of data) {
        if (item.type === "group" && item.value.length == 0) {
          // alert("目标参数有条件组没有添加条件,请将条件设置完整!!");
          // this.$alert(
          //   this.$t("ruleCreate.assertsGroupNoConditionTips"),
          //   this.$t("ruleCreate.tips")
          // );
          this.$message({
            type: "warning",
            message: this.$t("ruleCreate.assertsGroupNoConditionTips"),
          });
          this.checkAssertsValue = false;
          return;
        }
        if (item.type === "meta") {
          const index1 = item.tableHeader.split("_").at(-1);
          const index2 = item.value.includes("Col_")
            ? item.value.split("_").at(-1)
            : "notCol";
          if (index1 && !this.form.tableHeaders[index1]) {
            this.$message({
              type: "warning",
              message: "Col_" + index1 + " 表头不存在,请修改填写项！",
            });
            this.checkAssertsValue = false;
            return;
          }
          if (index2 !== "notCol" && !this.form.tableHeaders[index2]) {
            this.$message({
              type: "warning",
              message: "Col_" + index2 + " 表头不存在,请修改所在填写项！",
            });
            this.checkAssertsValue = false;
            return;
          }
        }
        if (
          item.type === "meta" &&
          (item.tableHeader === "" ||
            item.operator === "" ||
            (this.isShowInput(item.operator) && item.value === ""))
        ) {
          // alert("目标参数有操作符未选择,请检查所有填写项是否均已填写!!");
          // this.$alert(
          //   "目标参数条件未填写完成，请检查所有填写项是否均已填写！",
          //   this.$t("ruleCreate.tips")
          // );
          this.$message({
            type: "warning",
            message: "目标参数条件未填写完成，请检查所有填写项是否均已填写！",
          });
          this.checkAssertsValue = false;
          return;
        }
        this.checkAssertsValue = true;

        if (item.value && item.value.length > 0) {
          this.checkAssertsData(item.value);
        }
      }
    },
    // 验证表头选择数据
    checkTableHeaders(data) {
      if (data <= 0) {
        this.$message({
          type: "warning",
          message: "请先添加表头选择！",
        });
        return false;
      }
      for (let index = 0; index < data.length; index++) {
        const item = data[index];
        if (item.tableName === "" || item.sheet === "" || item.header === "") {
          this.$message({
            type: "warning",
            message: "表头选项未填写完成，请检查所有填写项是否均已填写！",
          });
          return false;
        }
      }
      return true;
    },

    // 处理表单数据方法
    handleFormData(data) {
      for (const item of data) {
        if (item.type === "group" && item.operator === "and") {
          item.prefix = "true";
          item.operator = "and";
        }
        if (item.type === "group" && item.operator === "or") {
          item.prefix = "true";
          item.operator = "or";
        }
        if (item.type === "group" && item.operator === "not and") {
          item.prefix = "false";
          item.operator = "and";
        }
        if (item.type === "group" && item.operator === "not or") {
          item.prefix = "false";
          item.operator = "or";
        }
        if (item.type === "meta" && item.operator === "range") {
          if (item.operatorType === 0) item.value = [+item.value];
          if (item.operatorType === 1) item.value = item.value.split();
        }

        if (item.value && item.value.length > 0) {
          this.handleFormData(item.value);
        }
      }
    },
    // 获取表单规则数据
    getRuleData() {
      let formData = "";
      this.$refs.form.validate((valid) => {
        if (!valid) {
          this.$message({
            type: "warning",
            message: this.$t("ruleCreate.requiredNotEmptyTips"),
          });
          return;
        } else {
          if (!this.form.projectBranch) {
            this.$message.warning(this.$t("ruleCreate.selectBranch"));
            return;
          }
          // 验证是否已添加表头
          if (!this.checkTableHeaders(this.form.tableHeaders)) return;
          if (!this.checkCalc(this.form.calc)) return;
          if (!this.checkMergeParams()) return;

          // 定义一个常量保存表单数据
          const rulesData = JSON.parse(JSON.stringify(this.tableRulesForm));
          // 没有任何条件参数点击提交的弹窗提示
          if (rulesData.asserts && rulesData.asserts.length == 0) {
            // alert("请在目标参数至少添加一个条件组!!");
            this.$message({
              type: "warning",
              message: this.$t("ruleCreate.targetParameterAddConditionTips"),
            });
            return;
          }
          // 调用验证表单数据的方法
          this.checkAssertsData(rulesData.asserts);
          // 验证值为false则退出提交事件 并且把赋值还原为true
          if (!this.checkAssertsValue) {
            this.checkAssertsValue = true;
            return;
          }

          if (rulesData.conditions && rulesData.conditions.length > 0) {
            this.checkConditionsData(rulesData.conditions);
          }
          if (!this.checkConditionsValue) {
            this.checkConditionsValue = true;
            return;
          }
          // 调用处理表单数据的方法
          // this.handleFormData(rulesData.asserts);
          // this.handleFormData(rulesData.conditions);
          // 处理需要导出JSON的数据
          const {
            name,
            ruleLevel,
            isEnabled,
            tableHeaders,
            calc,
            mergeParams,
            description,
          } = this.form;
          const { conditions, asserts } = rulesData;
          const condition = conditions[0] ? conditions[0] : {};
          const assert = asserts[0] ? asserts[0] : {};
          formData = {
            isEnabled,
            name,
            ruleLevel,
            tableHeaders,
            condition,
            calc,
            mergeParams,
            assert,
            description,
          };
        }
      });
      return formData;
    },
    // 测试规则事件
    async testRule() {
      let formData = this.getRuleData();
      if (!formData) return;
      let { code, data, msg } = await testExcelRule({
        projectId: this.form.projectId,
        projectBranch: this.form.projectBranch,
        ruleForm: JSON.stringify(formData),
      });
      if (code === 200) {
        this.testRuleData = ""; //先清空上次的测试结果
        this.testRuleData = data.output;
      } else {
        this.$message.error(msg);
      }
      // this.testRuleData = "测试结果信息";
    },

    // 下载规则JSON文件
    onDownload() {
      let formData = this.getRuleData();
      if (!formData) return;
      // 导出JSON文件;
      const blob = new Blob([JSON.stringify(formData, null, 2)], {
        type: "application/json",
      });
      const name = formData.name;
      // 保存json文件 文件名称使用规则名称
      this.$confirm(
        this.$t("ruleCreate.isExportJsonFile"),
        this.$t("ruleCreate.tips"),
        {
          type: "warning",
        }
      )
        .then(async () => {
          // 保存json文件 文件名称使用规则名称
          FileSaver.saveAs(blob, name);
          this.$message.success(this.$t("ruleCreate.downloadStarted"));
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: this.$t("ruleCreate.exportCanceled"),
          });
        });
    },
    // 提交表单事件方法
    onSubmit() {
      let formData = this.getRuleData();
      if (!formData) return;
      // 保存json文件 文件名称使用规则名称
      this.$confirm(
        this.$t("ruleCreate.isSaveRule"),
        this.$t("ruleCreate.tips"),
        {
          type: "warning",
        }
      )
        .then(async () => {
          // console.log(JSON.stringify(formData));
          // 保存规则请求方法
          let { code, msg } = await createExcelRule({
            projectId: this.form.projectId,
            branch: this.form.projectBranch,
            ruleForm: JSON.stringify(formData),
          });
          if (code === 200) {
            this.$message({
              type: "success",
              message: this.$t("ruleCreate.saved"),
            });
            this.clearFormData();
          } else {
            this.$message.error(msg);
          }
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: this.$t("ruleCreate.saveCanceled"),
          });
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.rule-form {
  background-color: #fff;
  padding: 15px;
  padding-top: 25px;
}
::v-deep.test-result .el-textarea__inner {
  background-color: #edeff2 !important;
  color: #606266;
}
::v-deep.el-button--mini.is-circle {
  padding: 3px;
}

::v-deep.el-button--mini {
  padding: 6px 10px;
}
</style>
