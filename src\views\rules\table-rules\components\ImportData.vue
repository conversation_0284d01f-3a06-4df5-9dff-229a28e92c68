<template>
  <div>
    <!-- <el-button type="primary" @click="importData"
      >{{ $t("ruleCreate.importRule")
      }}<i class="el-icon-upload el-icon--right"></i
    ></el-button> -->
    <el-dialog
      :visible.sync="dialogImport"
      :title="$t('ruleCreate.import')"
      width="40%"
      append-to-body
    >
      <div style="text-align: center">
        <!-- 此处action后续需改为有效的接口——当前为组件的官网范例接口 -->
        <el-upload
          drag
          :limit="1"
          ref="upload"
          action=""
          accept=".json"
          :file-list="fileList"
          :on-change="fileChang"
          :on-remove="onRemove"
          :on-exceed="handleExceed"
          :auto-upload="false"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">
            {{ $t("ruleCreate.uploadTips1")
            }}<em>{{ $t("ruleCreate.uploadTips2") }}</em>
          </div>
          <div class="el-upload__tip" slot="tip">
            {{ $t("ruleCreate.uploadJson") }}
          </div>
        </el-upload>
      </div>
      <span slot="footer">
        <el-button @click="dialogImport = false" size="mini">{{
          $t("ruleCreate.cancel")
        }}</el-button>
        <el-button @click="importConfirm" size="mini" type="primary">{{
          $t("ruleCreate.ok")
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// 引入规则选项框的规则数据设置文件
import { isArray } from "@/utils/validate";
import { mapGetters } from "vuex";
import { getExcelRuleConfig } from "@/api/rule";

export default {
  data() {
    return {
      projectData: {}, //保存项目信息

      fileList: [], // 上传的文件显示在下方列表
      uploadData: [], // 保存导入的JSON数据
      dialogImport: false, // 导入界面弹出开关
      jsonData: null, //传递给父组件的规则数据
      stypeArr: [], //上传的数据所有的stype
      key: Date.now(),
    };
  },

  methods: {
    // 获取表格名对应配置
    async getRuleConfig() {
      if (!this.projectData.stype)
        return this.$message.warning("缺少表格名,请检查文件!");
      const params = {
        table_name: this.projectData.stype,
        projectId: this.projectData.projectId,
        projectBranch: this.projectData.projectBranch,
      };
      const { code, rule_config, msg } = await getExcelRuleConfig(params);
      if (code !== 200) return this.$message.error(msg);
      this.$store.commit("tableCondition/setRuleConfig", rule_config.values);
    },
    // 文件上传时的钩子
    fileChang(file, fileList) {
      // 调用文件读取FileReader对象的方法
      let reader = new FileReader();
      // 将文件读取为文本
      reader.readAsText(file.raw);
      reader.onload = (e) => {
        this.uploadData = [];
        // 反序列化json数据保存下来
        this.uploadData = JSON.parse(e.target.result);
        // this.projectData.stype = this.uploadData.stype;
        // this.getRuleConfig();
      };
    },
    // 处理上传上来的数据
    handleJsonData(data) {
      for (const item of data) {
        if ((item.type === "group" || item.type === "meta") && !item.key) {
          // console.log("没有key");
          item.key = this.key--;
        }

        // 如果item.value存在并且是数组，就再调用一次函数，传数组给他，直到没有value数组结束
        if (item.value && item.value.length > 0) {
          this.handleJsonData(item.value);
        }
      }
    },

    // 控制导入弹窗的开关事件
    importData(data) {
      if (!data.projectId && !data.projectBranch)
        return this.$message.warning(this.$t("ruleCreate.selectProjectBranch"));
      this.projectData = data;
      this.dialogImport = true;
    },

    // 上传文件超出文件数量限制/文件格式不符合要求时
    handleExceed(files, fileList) {
      this.$message.warning(this.$t("ruleCreate.onlyJsonTips"));
    },

    // 移除文件
    onRemove(file) {
      this.fileList = [];
    },

    // 导入确认
    importConfirm() {
      if (!this.uploadData.assert) {
        this.$alert(
          this.$t("ruleCreate.noDataTips"),
          this.$t("ruleCreate.tips"),
          {
            confirmButtonText: this.$t("ruleCreate.ok"),
            callback: (action) => {
              this.$message({
                type: "info",
                message: this.$t("ruleCreate.fileRemoved"),
              });
              this.fileList = [];
            },
          }
        );
        // alert("没有数据，请重新选择文件上传！");
        return;
      }
      this.$confirm(
        this.$t("ruleCreate.coverageDataTips"),
        this.$t("ruleCreate.tips"),
        {
          type: "warning",
        }
      ).then(() => {
        // 拷贝上传的数据
        this.jsonData = JSON.parse(JSON.stringify(this.uploadData));
        // 调用处理数据方法
        this.handleJsonData([this.jsonData.assert]);
        this.handleJsonData([this.jsonData.condition]);

        // 检查上传的对象数据是否有isEnabled 没有就添加默认值为true
        if (!this.jsonData.hasOwnProperty("isEnabled")) {
          // console.log("没有isEnabled");
          this.jsonData.isEnabled = true;
        }
        if (!this.jsonData.hasOwnProperty("ruleLevel")) {
          // console.log("没有ruleLevel");
          this.jsonData.ruleLevel = "normal";
        }
        this.$store.commit("tableCondition/setUploadData", this.jsonData);
        // this.$parent.$parent.$parent.clearFormData();

        this.dialogImport = false;

        this.$message({
          type: "success",
          message: this.$t("ruleCreate.importSucceeded"),
        });
        this.onRemove();
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
