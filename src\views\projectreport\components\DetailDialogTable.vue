<template>
  <div>
    <!-- 分页器 -->
    <!-- <div style="display: flex; justify-content: end" v-if="total > 0">
      <Pagination
        style="margin-top: 10px; padding: 10px 20px"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="pageSwitch"
        :auto-scroll="listQuery.limit >= 30"
      />
    </div> -->
    <!-- 详情弹窗 -->
    <el-dialog
      v-el-drag-dialog
      :visible.sync="dialogTableVisible"
      :title="$t('reportDetail.detailedData')"
      width="65%"
    >
      <el-table :data="tableList" border max-height="550px">
        <!-- <el-table-column
          width="200px"
          v-for="(item, index) in tableTitleArr"
          :key="index"
          :prop="item"
          :label="item"
        /> -->
        <el-table-column type="expand">
          <template slot-scope="props">
            <el-descriptions :column="2">
              <el-descriptions-item
                v-for="(item, index) in Object.keys(props.row)"
                :key="index"
                :label-style="{ fontWeight: '600' }"
                :label="item"
                >{{ props.row[item] }}</el-descriptions-item
              >
            </el-descriptions>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="name" />
        <el-table-column prop="type" label="type" />
        <el-table-column prop="path" label="path" />
        <el-table-column prop="errorList" label="errorList" />
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogTableVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import elDragDialog from "@/directive/el-drag-dialog";
import Pagination from "@/components/Pagination/index.vue";

export default {
  name: "ReportDetail",
  components: { Pagination },
  directives: { elDragDialog },
  props: ["reportDetail", "tableTitle"],
  data() {
    return {
      total: 0,
      tableList: [],
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 30,
      },
      dialogTableVisible: false,
      tableTitleArr: [],
    };
  },

  mounted() {},
  methods: {
    // 是否省略提示
    handleShowTooltip(event) {
      let cell = event.target.querySelector(".el-tooltip");
      this.showTooltip = cell.scrollWidth === cell.offsetWidth;
    },
    openDialog(val) {
      let data = JSON.parse(JSON.stringify(val));
      this.tableTitleArr = Object.keys(data.report[0]);
      this.tableList = data.report;
      this.dialogTableVisible = true;
    },
    // 分页器页码切换 获取页码和页数
    // pageSwitch(val) {
    //   this.getList();
    // },
  },
};
</script>

<style lang="scss" scoped>
.filter-type {
  display: flex;
  vertical-align: middle;
}

.input-with-select .el-input-group__prepend {
  background-color: #fff;
}

::v-deep .el-dialog__body {
  padding: 0 20px 20px 20px;
}

::v-deep .el-menu-item.is-active {
  background-color: #e8f4ff;
}

// ----------修改elementui表格的默认样式-----------
::v-deep .el-table__body-wrapper {
  // cursor: pointer;

  &::-webkit-scrollbar {
    height: 6px;
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    // 轨道颜色
    background-color: #fff;
  }

  &::-webkit-scrollbar-thumb {
    // 滚动块颜色
    background-color: #e6e9ed;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    // hover高亮
    background-color: #d5d8db;
  }
}

// 当表格没有滚动条时
::v-deep .el-table__body-wrapper.is-scrolling-none ~ .el-table__fixed-right {
  height: 100% !important;
  bottom: 0 !important;
  box-shadow: none !important;
}

// 当表格有纵向滚动条时
::v-deep .el-table--scrollable-y .el-table__fixed-right {
  right: 6px !important;
}

// 当表格只有横向滚动条，没有纵向滚动条时
::v-deep
  .el-table--scrollable-x:not(.el-table--scrollable-y)
  .el-table__fixed-right {
  right: 0 !important;
}
</style>
