<template>
  <el-card style="margin-bottom: 20px">
    <div slot="header" class="clearfix">
      <span>{{ $t("profile.personalInformation") }}</span>
    </div>
    <div style="padding: 10px">
      <div class="user-profile">
        <div class="box-center">
          <el-upload
            class="avatar-uploader"
            :http-request="uploadAvatar"
            action=""
            accept=".jpg,.jpeg,.png,.gif,.webp"
            :show-file-list="false"
            :on-change="fileChang"
          >
            <img
              :src="imageUrl !== '' ? imageUrl : user.avatar"
              class="avatar"
            />
            <div class="upload-handle" @click.stop>
              <div class="handle-icon" @click="editImg">
                <i class="el-icon-edit-outline" style="padding-bottom: 4px"></i>
                <span>{{ $t("uploadImage.change") }}</span>
              </div>
              <div class="handle-icon" @click="imgViewVisible = true">
                <i class="el-icon-zoom-in" style="padding-bottom: 4px"></i>
                <span>{{ $t("uploadImage.check") }}</span>
              </div>
            </div>
          </el-upload>
        </div>
        <div class="box-center">
          <div class="user-name text-center">{{ name }}</div>
          <!-- <div class="user-role text-center text-muted">
            这个家伙很懒，什么都没有留下
          </div> -->
        </div>
      </div>

      <div class="user-bio">
        <!-- <div class="user-education user-bio-section">
        <div class="user-bio-section-header">
          <svg-icon icon-class="education" /><span>个人简介</span>
        </div>
        <div class="user-bio-section-body">
          <div class="text-muted">这个家伙很懒，什么都没有留下</div>
        </div>
      </div>

      <div class="user-skills user-bio-section">
        <div class="user-bio-section-header">
          <i class="el-icon-user" /><span>个人信息</span>
        </div>
        <div class="user-bio-section-body">
          <div class="progress-item">
            <span>Vue</span>
            <el-progress :percentage="70" />
          </div>
          <div class="progress-item">
            <span>JavaScript</span>
            <el-progress :percentage="18" />
          </div>
          <div class="progress-item">
            <span>Css</span>
            <el-progress :percentage="12" />
          </div>
          <div class="progress-item">
            <span>ESLint</span>
            <el-progress :percentage="100" status="success" />
          </div>
        </div>
      </div> -->
        <div class="user-bio-section-body">
          <div class="user-item">
            <i class="el-icon-key" /><span>{{ user.role }}</span>
          </div>
          <div class="user-item">
            <i class="el-icon-phone-outline" /><span>{{ user.phone }}</span>
          </div>
          <div class="user-item">
            <i class="el-icon-message" /><span>{{ user.email }}</span>
          </div>
          <!-- <div class="user-item">
            <i class="el-icon-office-building" /><span>公司 - 部门</span>
          </div>
          <div class="user-item">
            <i class="el-icon-location-outline" /><span
              >中国 • 广东省 • 深圳市</span
            >
          </div> -->
        </div>
      </div>
    </div>
    <!-- 查看头像 -->
    <el-image-viewer
      v-if="imgViewVisible"
      :on-close="closeImgViewer"
      :url-list="[imageUrl !== '' ? imageUrl : user.avatar]"
    />
  </el-card>
</template>

<script>
import PanThumb from "@/components/PanThumb";
import { uploadAvatar } from "@/api/upload";

export default {
  components: {
    PanThumb,
    "el-image-viewer": () =>
      import("element-ui/packages/image/src/image-viewer"),
  },
  props: ["user"],
  data() {
    return {
      imageUrl: "",
      imgViewVisible: false, //查看头像
    };
  },
  computed: {
    name() {
      return this.$store.getters.name;
    },
  },
  methods: {
    // 查看图片
    closeImgViewer() {
      this.imgViewVisible = false;
    },
    // 更换图片
    editImg() {
      const dom = document.querySelector(`.el-upload__input`);
      dom && dom.dispatchEvent(new MouseEvent("click"));
    },

    // 获取上传头像的url
    fileChang(file, fileList) {
      // 上传头像前检查格式
      const isJPG =
        file.raw.type === "image/jpeg" ||
        file.raw.type === "image/png" ||
        file.raw.type === "image/webp" ||
        file.raw.type === "image/gif";
      const isLt2M = file.raw.size / 1024 / 1024 < 2;
      if (!isJPG) {
        this.$message.error(this.$t("profile.isJPG"));
      }
      if (!isLt2M) {
        this.$message.error(this.$t("profile.isLt2M"));
      }
      if (!isJPG || !isLt2M) return;
      this.imageUrl = URL.createObjectURL(file.raw);
    },
    // 上传头像
    async uploadAvatar(uploadFile, userId = this.user.userId) {
      if (!uploadFile) return;
      const form = new FormData(); // FormData 对象
      form.append("photo", uploadFile.file);
      form.append("userId", userId);
      let { code, msg } = await uploadAvatar(form);
      if (code === 200) {
        let avatar = `${
          process.env.VUE_APP_BASE_API
        }/media/avatar/${userId}.png?time=${Date.now()}`;
        this.$store.commit("user/SET_AVATAR", avatar);
        this.$message.success(this.$t("profile.uploadSuccess"));
      }
      if (code !== 200) this.$message.error(msg);
    },
  },
};
</script>

<style lang="scss" scoped>
.box-center {
  margin: 0 auto;
  display: table;
}

.avatar-uploader ::v-deep.el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader ::v-deep.el-upload:hover {
  border-color: #409eff;
  border: 1px dashed #d9d9d9;
  .upload-handle {
    opacity: 1;
  }
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}
.avatar {
  width: 100px;
  height: 100px;
  display: block;
}
.upload-handle {
  position: absolute;
  top: 0;
  right: 0;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  cursor: pointer;
  background: rgb(0 0 0 / 60%);
  opacity: 0;
  transition: var(--el-transition-duration-fast);
  .handle-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0 6%;
    color: aliceblue;
    .el-icon {
      margin-bottom: 40%;
      font-size: 130%;
      line-height: 130%;
    }
    span {
      font-size: 85%;
      line-height: 85%;
    }
  }
}

.text-muted {
  color: #777;
}

.user-profile {
  .user-name {
    font-weight: bold;
  }

  .box-center {
    padding-top: 10px;
  }

  .user-role {
    padding-top: 10px;
    font-weight: 400;
    font-size: 14px;
  }

  .box-social {
    padding-top: 30px;

    .el-table {
      border-top: 1px solid #dfe6ec;
    }
  }

  .user-follow {
    padding-top: 20px;
  }
}

.user-bio {
  margin-top: 20px;
  color: #606266;

  span {
    padding-left: 4px;
  }

  .user-bio-section-body {
    .user-item {
      padding: 10px 0;
      font-size: 14px;
    }
  }

  .user-bio-section {
    font-size: 14px;
    padding: 15px 0;

    .user-bio-section-header {
      border-bottom: 1px solid #dfe6ec;
      padding-bottom: 10px;
      margin-bottom: 10px;
      font-weight: bold;
    }
  }
}
</style>
