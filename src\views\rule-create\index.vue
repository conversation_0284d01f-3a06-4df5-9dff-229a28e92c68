<template>
  <div class="app-container">
    <div class="rule-form">
      <div class="el-page-header">
        <div class="el-page-header__content">
          {{ $t("ruleCreate.ruleCreate") }}
        </div>
      </div>
      <el-divider />

      <el-form ref="form" :model="form" :rules="rules" label-width="95px">
        <el-form-item :label="$t('ruleCreate.projectBranch')" prop="projectId">
          <div
            style="
              display: flex;
              justify-content: space-between;
              align-items: center;
            "
          >
            <div>
              <el-select
                v-model="form.projectId"
                :placeholder="$t('ruleCreate.selectProject')"
                style="width: 200px; margin-right: 10px"
                @change="changeProject"
              >
                <el-option
                  v-for="(item, index) in userProjectList"
                  :key="index"
                  :label="item.projectName"
                  :value="item.projectId"
                />
              </el-select>
              <el-select
                v-model="form.projectBranch"
                :placeholder="$t('ruleCreate.selectBranch')"
                style="width: 150px"
                @change="changeBranch"
              >
                <el-option
                  v-for="(item, index) in projectBranchData"
                  :key="index"
                  :label="item.branch"
                  :value="item.branch"
                />
              </el-select>
            </div>

            <el-radio-group
              v-model="form.isEnabled"
              :fill="form.isEnabled ? '#13ce66' : '#F56C6C'"
            >
              <el-radio-button :label="true">{{
                form.isEnabled
                  ? $t("ruleCreate.enabled")
                  : $t("ruleCreate.enable")
              }}</el-radio-button>
              <el-radio-button :label="false">{{
                form.isEnabled
                  ? $t("ruleCreate.ignore")
                  : $t("ruleCreate.ignored")
              }}</el-radio-button>
            </el-radio-group>
          </div>
        </el-form-item>
        <el-form-item :label="$t('ruleCreate.ruleCategories')" prop="stype">
          <el-tooltip
            class="item"
            effect="dark"
            :content="$t('ruleCreate.switchTypeTips')"
            placement="top"
          >
            <el-select
              v-model="form.stype"
              name="stype"
              :placeholder="$t('ruleCreate.chooseRuleType')"
              style="width: 250px; margin-right: 10px"
            >
              <el-option
                v-for="(item, index) in ruleConfig"
                :key="item.type"
                :label="item.type"
                :value="item.type"
                @click.capture.native="getStypeIndex(item.type, index)"
              />
            </el-select>
          </el-tooltip>
          <el-tooltip
            class="item"
            effect="dark"
            :content="'选择优先级'"
            placement="top"
          >
            <el-select
              v-model="form.ruleLevel"
              name="ruleLevel"
              :placeholder="'选取优先级'"
              style="width: 120px"
            >
              <el-option label="高" value="high"></el-option>
              <el-option label="中" value="middle"></el-option>
              <el-option label="低" value="low"></el-option>
            </el-select>
          </el-tooltip>
        </el-form-item>
        <el-form-item :label="$t('ruleCreate.ruleName')" prop="name">
          <el-input
            v-model="form.name"
            :placeholder="$t('ruleCreate.enterName')"
            :clearable="true"
            style="width: 250px"
          />
        </el-form-item>
        <el-form-item :label="$t('ruleCreate.conditionParameter')">
          <div>
            <el-button
              v-if="rulesForm.conditions.length === 0"
              icon="el-icon-plus"
              type="primary"
              size="mini"
              style="margin-left: 0"
              @click="addFirstConditionGroup('condition')"
              >{{ $t("ruleCreate.addConditionGroup") }}</el-button
            >
            <Conditions v-else :conditions="rulesForm.conditions" />
          </div>
        </el-form-item>
        <el-form-item
          :label="$t('ruleCreate.targetParameter')"
          :rules="{ required: true }"
        >
          <div>
            <el-button
              v-if="rulesForm.asserts.length === 0"
              icon="el-icon-plus"
              type="primary"
              size="mini"
              style="margin-left: 0"
              @click="addFirstConditionGroup('assert')"
              >{{ $t("ruleCreate.addConditionGroup") }}</el-button
            >
            <Conditions v-else :conditions="rulesForm.asserts" />
          </div>
        </el-form-item>

        <el-form-item size="large">
          <div style="display: flex; margin-top: 15px">
            <el-button type="info" @click="testRule()"
              >{{ $t("ruleCreate.testRule")
              }}<i class="el-icon-document-checked el-icon--right"></i
            ></el-button>
            <!-- 引入规则导入子组件 -->
            <div style="margin: 0 10px">
              <import-data></import-data>
            </div>
            <el-button type="primary" @click="onDownload"
              >{{ $t("ruleCreate.downloadRule")
              }}<i class="el-icon-download el-icon--right"></i
            ></el-button>
            <el-button
              type="success"
              @click="onSubmit"
              style="margin-left: 10px"
              >{{ $t("ruleCreate.saveRule")
              }}<i class="el-icon-circle-check el-icon--right"></i
            ></el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <!-- 测试规则弹窗展示数据 -->
    <test-rule-modal
      projectType="resource"
      :testRuleVisible="testRuleVisible"
      :testRuleData="testRuleData"
      @closeTestRule="closeTestRule"
    ></test-rule-modal>
  </div>
</template>

<script>
// 引入规则选项框的规则数据设置文件
// import ruleConfig from "@/assets/rule_config.json";
// 引入导出JSON文件的插件
import FileSaver from "file-saver";
import Conditions from "./components/Conditions.vue";
import ImportData from "./components/ImportData.vue";
import { mapGetters } from "vuex";
import TestRuleModal from "@/components/TestRuleModal/index.vue";
import { createRule, testRule, getRuleConfig } from "@/api/rule";
import { getProjectBranchs, getUserProjectList } from "@/api/project";

export default {
  components: { Conditions, ImportData, TestRuleModal },

  data() {
    return {
      userProjectList: [],
      projectBranchData: [],

      key: Date.now(), //条件绑定的key
      checkConditionsValue: true, // 保存验证表单条件数据是否通过
      checkAssertsValue: true, // 保存验证表单条件数据是否通过
      // ruleConfig: [], // 引入的规则选项设置
      // stypeIndex: 0, // 规则类型选项对应的下一个的选项值(规则类型在ruleConfig的索引值)
      operatorType: 1, // 条件的对应操作符类 值有:0 1 2 三种
      operatorValue: null, // 操作符对应的值

      conditionKey: null, // 条件对应的key
      conditionGroupKey: null, // 条件组对应的key

      // 保存所有表单的数据
      form: {
        projectId: void 0,
        projectBranch: "",
        stype: "",
        ruleLevel: "middle",
        name: "",
        isEnabled: true,
      },

      rules: {
        //规则类型和名称的验证规则
        projectId: [
          {
            required: true,
            message: this.$t("ruleCreate.projectBranchRequired"),
            trigger: "change",
          },
        ],
        stype: [
          {
            required: true,
            message: this.$t("ruleCreate.ruleCategoriesRequired"),
            trigger: "change",
          },
        ],
        name: [
          {
            required: true,
            message: this.$t("ruleCreate.ruleNameRequired"),
            trigger: "blur",
          },
          {
            pattern: /^[^\?\:\*\"\<\>\|\/\\\？\：\“\”]*$/,
            message: '名称不能包含\\ / : * ? " < > |',
            trigger: "change",
          },
        ],
      },

      testRuleVisible: false, //测试规则弹窗
      testRuleData: [],
    };
  },
  computed: {
    ...mapGetters(["rulesForm", "ruleConfig"]),
  },

  mounted() {
    this.getUserProjectList();
    this.form.stype = this.rulesForm.stype;
    this.form.ruleLevel = this.rulesForm.ruleLevel;
    this.form.name = this.rulesForm.name;
    this.form.isEnabled = this.rulesForm.isEnabled;
  },
  created() {
    //在页面刷新时将form数据保存到localStorage里
    window.addEventListener("beforeunload", () => {
      localStorage.setItem("rule_project", JSON.stringify(this.form));
      const formData = {
        stype: this.form.stype,
        ruleLevel: this.form.ruleLevel,
        name: this.form.name,
        isEnabled: this.form.isEnabled,
        conditions: this.rulesForm.conditions,
        asserts: this.rulesForm.asserts,
      };
      localStorage.setItem("rule_form", JSON.stringify(formData));
      // localStorage.setItem("rule_form", JSON.stringify(this.rulesForm));
    });
  },
  //在页面离开时记录form数据
  beforeRouteLeave(to, from, next) {
    localStorage.setItem("rule_project", JSON.stringify(this.form));
    const formData = {
      stype: this.form.stype,
      ruleLevel: this.form.ruleLevel,
      name: this.form.name,
      isEnabled: this.form.isEnabled,
      conditions: this.rulesForm.conditions,
      asserts: this.rulesForm.asserts,
    };
    localStorage.setItem("rule_form", JSON.stringify(formData));
    next();
  },

  watch: {
    // 监控表单数据变化
    "rulesForm.stype"(newValue, oldValue) {
      this.form.stype = newValue;
    },
    "rulesForm.ruleLevel"(newValue, oldValue) {
      this.form.ruleLevel = newValue;
    },
    "rulesForm.name"(newValue, oldValue) {
      this.form.name = newValue;
    },
    "rulesForm.isEnabled"(newValue, oldValue) {
      this.form.isEnabled = newValue;
    },
    form: {
      handler(newVal) {
        this.$store.commit("conditions/setFormData", newVal);
      },
      deep: true,
    },
  },

  methods: {
    // 获取规则分类
    async getRuleConfig(projectId, branch) {
      if (!projectId) {
        this.$message.error(this.$t("ruleCreate.selectProject"));
        return;
      }
      if (!branch) {
        this.$message.error(this.$t("ruleCreate.missingBranch"));
        return;
      }
      let { code, json } = await getRuleConfig({ projectId, branch });
      if (code === 200) {
        if (json.length <= 0) {
          this.$message.error(this.$t("ruleCreate.missingRuleConfig"));
        }
        this.$store.commit("conditions/setRuleConfig", json);
        this.form.stype = "";
        // this.$refs.form.clearValidate();

        if (!localStorage.getItem("rule_form")) {
          let stypeIndex = (this.ruleConfig || []).findIndex(
            (item) => item.type === this.form.stype
          );
          this.$store.commit("conditions/setStypeIndex", stypeIndex); //把获取的类型索引值保存
        } else {
          let formData = JSON.parse(localStorage.getItem("rule_form"));
          let stypeIndex = (this.ruleConfig || []).findIndex(
            (item) => item.type === formData.stype
          );
          this.$store.commit("conditions/setStypeIndex", stypeIndex);
          this.$store.commit("conditions/setFormData", formData);
          this.form.stype = formData.stype;
          this.form.name = formData.name;
          this.form.isEnabled = formData.isEnabled;
          localStorage.removeItem("rule_form");
        }
      }
    },
    // 重置from表单 用于导入规则子组件调用
    clearFormData() {
      this.form.stype = this.rulesForm.stype;
      this.form.name = this.rulesForm.name;
      this.form.isEnabled = this.rulesForm.isEnabled;
    },
    // 获取所有项目
    async getUserProjectList() {
      let { code, projectList, dataLen } = await getUserProjectList();
      if (code === 200) {
        this.userProjectList = projectList;
        if (localStorage.getItem("rule_project")) {
          let ruleProject = JSON.parse(localStorage.getItem("rule_project"));
          // 判断如果项目已被删除,则清除数据
          let findId = projectList.some((item) => {
            return item.projectId === ruleProject.projectId;
          });
          if (findId) {
            this.form = ruleProject;
            this.getProjectBranchs(this.form.projectId);
            localStorage.removeItem("rule_project");
          } else {
            this.$store.commit("conditions/setStypeIndex", -1);
            this.form.stype = "";
            // this.form.isEnabled = true;
            localStorage.removeItem("rule_project");
            localStorage.removeItem("rule_form");
          }
        }
      }
    },
    // 获取项目所有报告分支
    async getProjectBranchs(projectId) {
      let { code, data } = await getProjectBranchs({ projectId });
      if (code === 200) {
        this.projectBranchData = data;
        if (!this.form.projectBranch && data.length > 0) {
          this.form.projectBranch = this.projectBranchData[0].branch;
        }
        // 获取规则分类
        if (!this.form.projectId && !this.form.projectBranch) return;
        this.getRuleConfig(this.form.projectId, this.form.projectBranch); //获取规则配置
      }
    },
    // 切换项目重新获取分支
    changeProject(val) {
      this.form.projectBranch = "";
      this.getProjectBranchs(this.form.projectId);
    },
    // 切换分支
    changeBranch() {
      this.getRuleConfig(this.form.projectId, this.form.projectBranch); //获取规则配置
    },

    // 返回首页
    goHome() {
      this.$router.push("/");
    },

    // 当没有数据时先创建初始条件组的自定义事件
    addFirstConditionGroup(createType) {
      if (!this.form.stype) {
        this.$refs.form.validateField("stype");
        this.$message({
          type: "warning",
          message: this.$t("ruleCreate.ruleCategoriesRequired"),
        });
      } else {
        this.$store.commit("conditions/addFirstConditionGroup", createType);
      }
    },
    // 获取对应类型所在的索引值 切换类型清空数据
    getStypeIndex(stype, index) {
      let oldStyle = JSON.parse(JSON.stringify(this.form.stype));
      // console.log(stype, this.rulesForm.stype);
      if (
        !this.form.stype ||
        (this.rulesForm.asserts.length === 0 &&
          this.rulesForm.conditions.length === 0) ||
        oldStyle === stype
      ) {
        this.$store.commit("conditions/setStypeIndex", index); //把获取的类型索引值保存
        return;
      }

      if (oldStyle !== stype) {
        this.$confirm(
          this.$t("ruleCreate.switchTypeTips"),
          this.$t("ruleCreate.tips"),
          {
            type: "warning",
          }
        )
          .then(() => {
            this.$store.commit("conditions/setStypeIndex", index); //把获取的类型索引值保存
            this.$message({
              type: "success",
              message: this.$t("ruleCreate.switched"),
            });
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: this.$t("ruleCreate.cancelSwitch"),
            });
            this.form.stype = oldStyle;
          });
      }
    },
    // 验证输入的参数
    checkConditionsData(data) {
      for (const item of data) {
        if (item.type === "group" && item.value.length == 0) {
          // alert("条件参数有条件组没有添加条件,请将条件设置完整!!");
          this.$alert(
            this.$t("ruleCreate.conditionGroupNoConditionTips"),
            this.$t("ruleCreate.tips")
          );
          this.checkConditionsValue = false;
          return;
        }
        if (
          item.type === "meta" &&
          (item.operator === "" || item.operatorType === -1)
        ) {
          // alert("条件参数有操作符未选择,请检查所有填写项是否均已填写!!");
          this.$alert(
            this.$t("ruleCreate.conditionNoOperatorTips"),
            this.$t("ruleCreate.tips")
          );
          this.checkConditionsValue = false;
          return;
        }
        if (
          item.type === "meta" &&
          item.operator === "range" &&
          (item.value[0] === "" ||
            item.value[0] === null ||
            item.value[1] === "" ||
            item.value[1] === null)
        ) {
          // alert("条件参数的值设定不全,请检查所有填写项是否均已填写!!");
          this.$alert(
            this.$t("ruleCreate.conditionNoValueTips"),
            this.$t("ruleCreate.tips")
          );
          this.checkConditionsValue = false;
          return;
        }
        // 新增：校验in操作符的数值类型输入
        if (
          item.type === "meta" &&
          item.operator === "in" &&
          item.operatorType === 0 &&
          item.value !== "" &&
          item.value !== null
        ) {
          // 检查逗号分隔的值是否都能转换为数字
          const values = item.value.split(',').map(v => v.trim());
          const hasInvalidNumber = values.some(v => v !== "" && isNaN(+v));
          if (hasInvalidNumber) {
            this.$alert(
              `目标参数表单项中,${item.field} 集合中操作符的数值类型应为数字，请检查输入内容是否为有效数值`,
              this.$t("ruleCreate.tips")
            );
            this.checkConditionsValue = false;
            return;
          }
        }
        if (
          item.type === "meta" &&
          (item.value === null || item.value === "")
        ) {
          // alert("条件参数的值设定不全,请检查所有填写项是否均已填写!!");
          this.$alert(
            this.$t("ruleCreate.conditionNoValueTips"),
            this.$t("ruleCreate.tips")
          );
          this.checkConditionsValue = false;
          return;
        }

        if (item.value && item.value.length > 0) {
          this.checkConditionsData(item.value);
        }
      }
    },
    // 验证asserts数据方法
    checkAssertsData(data) {
      // if (data && data.length <= 0) {
      //   alert("请先添加条件组");
      //   return;
      // }
      for (const item of data) {
        if (item.type === "group" && item.value.length == 0) {
          // alert("目标参数有条件组没有添加条件,请将条件设置完整!!");
          this.$alert(
            this.$t("ruleCreate.assertsGroupNoConditionTips"),
            this.$t("ruleCreate.tips")
          );
          this.checkAssertsValue = false;
          return;
        }
        if (
          item.type === "meta" &&
          (item.operator === "" || item.operatorType === -1)
        ) {
          // alert("目标参数有操作符未选择,请检查所有填写项是否均已填写!!");
          this.$alert(
            this.$t("ruleCreate.assertsNoOperatorTips"),
            this.$t("ruleCreate.tips")
          );
          this.checkAssertsValue = false;
          return;
        }
        if (
          item.type === "meta" &&
          item.operator === "range" &&
          (item.value[0] === "" ||
            item.value[0] === null ||
            item.value[1] === "" ||
            item.value[1] === null)
        ) {
          // alert("目标参数的值设定不全,请检查所有填写项是否均已填写!!");
          this.$alert(
            this.$t("ruleCreate.assertsNoValueTips"),
            this.$t("ruleCreate.tips")
          );
          this.checkAssertsValue = false;
          return;
        }
        // 新增：校验in操作符的数值类型输入
        if (
          item.type === "meta" &&
          item.operator === "in" &&
          item.operatorType === 0 &&
          item.value !== "" &&
          item.value !== null
        ) {
          // 检查逗号分隔的值是否都能转换为数字
          const values = item.value.split(',').map(v => v.trim());
          const hasInvalidNumber = values.some(v => v !== "" && isNaN(+v));
          if (hasInvalidNumber) {
            this.$alert(
              `条件参数表单项中,${item.field} 集合中操作符的数值类型应为数字，请检查输入内容是否为有效数值`,
              this.$t("ruleCreate.tips")
            );
            this.checkAssertsValue = false;
            return;
          }
        }
        if (
          item.type === "meta" &&
          (item.value === null || item.value === "")
        ) {
          // alert("目标参数的值设定不全,请检查所有填写项是否均已填写!!");
          this.$alert(
            this.$t("ruleCreate.assertsNoValueTips"),
            this.$t("ruleCreate.tips")
          );
          this.checkAssertsValue = false;
          return;
        }

        if (item.value && item.value.length > 0) {
          this.checkAssertsData(item.value);
        }
      }
    },

    // 处理表单数据方法
    handleFormData(data) {
      for (const item of data) {
        if (item.type === "group" && item.operator === "and") {
          item.prefix = "true";
          item.operator = "and";
        }
        if (item.type === "group" && item.operator === "or") {
          item.prefix = "true";
          item.operator = "or";
        }
        if (item.type === "group" && item.operator === "not and") {
          item.prefix = "false";
          item.operator = "and";
        }
        if (item.type === "group" && item.operator === "not or") {
          item.prefix = "false";
          item.operator = "or";
        }
        // 处理 in 操作符：转换为对应类型的数组
        if (item.type === "meta" && item.operator === "in") {
          // 修改前：简单的数据处理
          // if (item.operatorType === 0) item.value = [+item.value];
          // if (item.operatorType === 1) item.value = item.value.split();
          
          // 修改后：支持逗号分隔的多个值
          if (item.operatorType === 0) {
            // 数值类型：按逗号分割，转换为数值数组
            item.value = item.value.split(',').map(v => +v.trim());
          } else if (item.operatorType === 1) {
            // 字符串类型：按逗号分割，去除空格
            item.value = item.value.split(',').map(v => v.trim());
          }
        }
        
        // 处理 range 操作符：转换为对应类型的数组
        if (item.type === "meta" && item.operator === "range") {
          if (item.operatorType === 0) {
            // 数值类型：转换数组元素为数值
            item.value = [+item.value[0], +item.value[1]];
          } else if (item.operatorType === 1) {
            // 字符串类型：保持字符串数组
            item.value = [item.value[0].toString(), item.value[1].toString()];
          }
        }
        
        // 处理其他操作符：根据 operatorType 转换单个值的类型
        if (item.type === "meta" && item.operator !== "in" && item.operator !== "range") {
          if (item.operatorType === 0) {
            // 数值类型：转换为数值
            item.value = +item.value;
          } else if (item.operatorType === 1) {
            // 字符串类型：确保为字符串
            item.value = item.value.toString();
          } else if (item.operatorType === 2) {
            // 布尔类型：确保为布尔值（通常已经是正确类型）
            item.value = Boolean(item.value);
          }
        }

        if (item.value && item.value.length > 0) {
          this.handleFormData(item.value);
        }
      }
    },
    // 获取表单规则数据
    getRuleData() {
      let formData = "";
      this.$refs.form.validate((valid) => {
        if (!valid) {
          this.$message({
            type: "warning",
            message: this.$t("ruleCreate.requiredNotEmptyTips"),
          });
          return;
        } else {
          if (!this.form.projectBranch) {
            this.$message.warning(this.$t("ruleCreate.selectBranch"));
            return;
          }
          // 定义一个常量保存表单数据
          const rulesData = JSON.parse(JSON.stringify(this.rulesForm));
          // 没有任何条件参数点击提交的弹窗提示
          if (rulesData.asserts && rulesData.asserts.length == 0) {
            // alert("请在目标参数至少添加一个条件组!!");
            this.$alert(
              this.$t("ruleCreate.targetParameterAddConditionTips"),
              this.$t("ruleCreate.tips")
            );
            return;
          }
          // 调用验证表单数据的方法
          this.checkAssertsData(rulesData.asserts);
          // 验证值为false则退出提交事件 并且把赋值还原为true
          if (!this.checkAssertsValue) {
            this.checkAssertsValue = true;
            return;
          }

          if (rulesData.conditions && rulesData.conditions.length > 0) {
            this.checkConditionsData(rulesData.conditions);
          }
          if (!this.checkConditionsValue) {
            this.checkConditionsValue = true;
            return;
          }
          // 调用处理表单数据的方法
          this.handleFormData(rulesData.asserts);
          this.handleFormData(rulesData.conditions);
          // 处理需要导出JSON的数据
          const { stype, ruleLevel, name, isEnabled } = this.form;
          const { conditions, asserts } = rulesData;
          const condition = conditions[0] ? conditions[0] : {};
          const assert = asserts[0] ? asserts[0] : {};
          formData = {
            stype,
            ruleLevel,
            isEnabled,
            name,
            condition,
            assert,
          };
        }
      });
      return formData;
    },
    // 测试规则事件
    async testRule() {
      let formData = this.getRuleData();
      if (!formData) return;
      let { code, data, msg } = await testRule({
        projectId: this.form.projectId,
        projectBranch: this.form.projectBranch,
        ruleForm: JSON.stringify(formData),
      });
      if (code === 200) {
        this.testRuleData = data;
        this.testRuleVisible = true;
      } else {
        this.$message.error(msg);
      }
    },
    // 关闭测试规则数据弹窗
    closeTestRule(val) {
      this.testRuleVisible = val;
    },

    // 下载规则JSON文件
    onDownload() {
      let formData = this.getRuleData();
      if (!formData) return;
      // 导出JSON文件;
      const blob = new Blob([JSON.stringify(formData, null, 2)], {
        type: "application/json",
      });
      const name = formData.name;
      // 保存json文件 文件名称使用规则名称
      this.$confirm(
        this.$t("ruleCreate.isExportJsonFile"),
        this.$t("ruleCreate.tips"),
        {
          type: "warning",
        }
      )
        .then(async () => {
          // 保存json文件 文件名称使用规则名称
          FileSaver.saveAs(blob, name);
          this.$message.success(this.$t("ruleCreate.downloadStarted"));
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: this.$t("ruleCreate.exportCanceled"),
          });
        });
    },
    // 提交表单事件方法
    onSubmit() {
      let formData = this.getRuleData();
      if (!formData) return;
      // 保存json文件 文件名称使用规则名称
      this.$confirm(
        this.$t("ruleCreate.isSaveRule"),
        this.$t("ruleCreate.tips"),
        {
          type: "warning",
        }
      )
        .then(async () => {
          // 保存规则请求方法
          let { code, msg } = await createRule({
            projectId: this.form.projectId,
            branch: this.form.projectBranch,
            ruleForm: JSON.stringify(formData),
          });
          if (code === 200) {
            this.$message({
              type: "success",
              message: this.$t("ruleCreate.saved"),
            });
          }
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: this.$t("ruleCreate.saveCanceled"),
          });
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.rule-form {
  background-color: #fff;
  padding: 15px;
  padding-top: 25px;
}
</style>
