<template>
  <div class="app-container-report-detail">
    <div class="report-detail">
      <!-- 页面头部，仅在非嵌入模式下显示 -->
      <!-- <template v-if="!embedded">
        <el-page-header
          :content="$t('reportDetail.reportDetail')"
          :title="$t('reportDetail.backToProjectReport')"
          @back="$router.back()"
        />
        <el-divider />
        <el-button
          style="position: absolute; top: 18px; right: 15px"
          type="danger"
          @click="deleteReport"
        >
          删除报告
        </el-button>
      </template> -->
      <!-- 分类tab -->
      <div>
        <el-tabs v-model="activeName" @tab-click="tabChange">
          <el-tab-pane label="资源类型" name="assets"> </el-tab-pane>
          <el-tab-pane label="报错类型" name="error"> </el-tab-pane>
        </el-tabs>
      </div>
      <!-- 资源分类 -->
      <div v-if="activeName === 'assets'">
        <div>
          <el-descriptions
            :title="$t('reportDetail.reportDetail')"
            :column="2"
            style="font-size: 16px"
          >
            <el-descriptions-item :label="$t('reportDetail.testId')">{{
              (reportData && reportData.id) || '-'
            }}</el-descriptions-item>
            <el-descriptions-item :label="$t('reportDetail.scanTime')">{{
              reportDetail.scan_time
            }}</el-descriptions-item>
            <el-descriptions-item :label="$t('reportDetail.testVersion')">{{
              reportDetail.branch
            }}</el-descriptions-item>
            <el-descriptions-item :label="$t('reportDetail.errorCount')">{{
              reportDataLen
            }}</el-descriptions-item>
          </el-descriptions>
        </div>
        <div style="margin-top: 20px">
          <report-detail
            :tableTitle="$t('reportDetail.reportList')"
            :projectId="projectId"
            :branch="reportDetail.branch"
            :reportId="reportData.id"
          />
        </div>
      </div>
      <!-- 报错类型分类 -->
      <div v-if="activeName === 'error'">
       <ErrorFilterTable :projectId="projectId"
        :reportId="reportData.id"
      ></ErrorFilterTable>
      </div>
    </div>
    <!-- 返回顶部组件 -->
    <el-tooltip placement="top" :content="$t('backToTop')">
      <back-to-top
        :visibility-height="300"
        :back-position="50"
        transition-name="fade"
      ></back-to-top>
    </el-tooltip>
  </div>
</template>

<script>
import ReportDetail from "./components/ReportDetail.vue";
import { getReportDetail, deleteReport,getReportDetailByError } from "@/api/project";
import BackToTop from "@/components/BackToTop";
import ErrorFilterTable from './components/errorFilterTable.vue'
export default {
  components: { ReportDetail, BackToTop,ErrorFilterTable },
  props: {
    // 报告数据，从父组件传入
    reportData: {
      type: Object,
      default: null
    },
    // 项目ID，从父组件传入
    projectId: {
      type: [String, Number],
      default: null
    },
    // 是否为嵌入模式（隐藏页面头部和删除按钮）
    embedded: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      reportDetail: {},
      activeName: "assets",
      reportList: [],
      reportDataLen: 0,
    };
  },
  mounted() {
    this.getReportDetail();
  },
  watch: {
    // 监听 props 变化，重新获取数据
    'reportData.id': {
      handler(newId) {
        if (newId) {
          this.getReportDetail();
        }
      },
      immediate: true
    }
  },

  methods: {
    // 获取报告详情
    async getReportDetail() {
      // 优先使用 props 传入的数据，其次使用路由参数
      let id = (this.reportData && this.reportData.id) || this.$route.query.reportId;
      let projectId = this.projectId || this.$route.query.projectId;

      if (!id) {
        console.warn('缺少报告ID');
        return;
      }

      if (!projectId) {
        console.warn('缺少项目ID');
        return;
      }

      const params = {
        projectId: projectId,
        id,
        pageNum: 1,
        pageSize: 30,
      };
      const { code, branch, data, dataLen } = await getReportDetail(params);
      if (code === 200) {
        this.reportDetail = branch;
        this.reportList = data;
        this.reportDataLen = dataLen;
      }
    },
    // deleteReport() {
    //   this.$confirm("确定删除该报告吗？", "提示", {
    //     confirmButtonText: "确定",
    //     cancelButtonText: "取消",
    //     type: "warning",
    //   }).then(async () => {
    //     const params = {
    //       projectId: this.$route.query.projectId,
    //       branch: this.reportDetail.branch,
    //       scan_time: this.reportDetail.scan_time,
    //       is_delete: true,
    //     };
    //     const { code, msg } = await deleteReport(params);
    //     if (code !== 200) return this.$message.error(msg);
    //     this.$message({
    //       type: "success",
    //       message: "删除成功!",
    //     });
    //     this.$router.back();
    //   });
    // },
    // 切换tab
    tabChange(val){
      console.log('val',val);
      
    }
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  .report-detail {
    background-color: #fff;
    padding: 15px;
    padding-top: 25px;
    position: relative;
  }
}
</style>
