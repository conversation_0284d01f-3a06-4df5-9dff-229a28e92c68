import request from '@/utils/request'
import Qs from 'qs'

export function getProjectList(query) {
  return request({
    url: '/get_projectList',
    method: 'get',
    params: query
  })
}

export function createProject(data) {
  data = Qs.stringify(data)
  return request({
    url: `/create_project`,
    method: 'post',
    data
  })
}

export function editProject(data) {
  data = Qs.stringify(data)
  return request({
    url: `/edit_project`,
    method: 'post',
    data
  })
}

export function deleteProject(data) {
  data = Qs.stringify(data)
  return request({
    url: `/delete_project`,
    method: 'post',
    data
  })
}

export function projectAddUser(data) {
  data = Qs.stringify(data)
  return request({
    url: `/project_add_user`,
    method: 'post',
    data
  })
}

export function projectDeleteUser(data) {
  data = Qs.stringify(data)
  return request({
    url: `/project_delete_user`,
    method: 'post',
    data
  })
}
