<template>
  <div>
    <h4 style="margin-bottom: 0">报错类型数据</h4>
    <div style="width: 400px; display: flex; margin: 0 0 10px auto">
      <el-input
        :placeholder="$t('reportDetail.searchKeyWords')"
        v-model="searchInput"
        clearable
        @keyup.enter.native="handleFilter"
        @clear="handleFilter"
      >
        <el-button
          slot="append"
          icon="el-icon-search"
          @click="handleFilter"
        ></el-button>
      </el-input>
    </div>
    <el-main style="padding: 0">
      <el-table :data="list" border max-height="800px">
        <!-- <el-table-column fixed prop="id" label="id" width="50" /> -->
        <el-table-column prop="check_result" label="检测结果" />
        <el-table-column fixed prop="rule_name" label="检测规则名称" />
        <el-table-column prop="effective_count" label="失败个数" />
        <el-table-column fixed="right" :label="$t('reportDetail.operation')">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="handleClick(scope.row)"
              >{{ $t("reportDetail.viewMore") }}</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-main>

    <!-- 分页器 -->
    <div style="display: flex; justify-content: end" v-if="total > 0">
      <Pagination
        style="margin-top: 10px; padding: 10px 20px"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="pageSwitch"
        :auto-scroll="listQuery.limit >= 30"
      />
    </div>
    <DetailDialogTable ref="detailDialogTableRef"></DetailDialogTable>
  </div>
</template>

<script>
import elDragDialog from "@/directive/el-drag-dialog";
import Pagination from "@/components/Pagination/index.vue";
import { getReportDetail } from "@/api/project";
import { getReportStat, getReportDetailByError } from "@/api/project";
import { getRuleConfig } from "@/api/rule";
import DetailDialogTable from "./DetailDialogTable.vue";
export default {
  name: "ReportDetail",
  components: { Pagination, DetailDialogTable },
  directives: { elDragDialog },
  // props: ["reportDetail", "tableTitle"],
    props: {
    tableTitle: {
      type: String,
      default: ""
    },
    projectId: {
      type: [String, Number],
      required: true
    },
    reportId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      ruleConfig: [], //保存规则分类
      reportTypeList: [],
      reportType: "",
      total: 0,
      list: [],
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 30,
      },
      showTooltip: false,
      searchInput: "",
    };
  },
  watch: {
    // 监听 projectId 变化
    projectId: {
      handler(newVal, oldVal) {
        if (newVal && newVal !== oldVal) {
           this.getDataByErrorData()          
        }
      },
      immediate: false
    },
    // 监听 reportId 变化
    reportId: {
      handler(newVal, oldVal) {
        if (newVal && newVal !== oldVal) {
         this.getDataByErrorData()
        }
      },
      immediate: false
    }

  },
  mounted() {
    this.searchInput = this.$route.query.keyword;
    // this.getRuleConfig();
    this.getDataByErrorData();
    // this.getReportStat();
  },
  methods: {
    // 选择类型
    handleSelect(key) {
      this.reportType = key;
      this.listQuery.page = 1;
      this.listQuery.limit = 30;
    },
    // 是否省略提示
    handleShowTooltip(event) {
      let cell = event.target.querySelector(".el-tooltip");
      this.showTooltip = cell.scrollWidth === cell.offsetWidth;
    },

    // 获取数据分类
    async getDataByErrorData() {
      // let id = this.$route.query.reportId;
      let id = this.reportId;
      if (!id) {
        // this.$router.push("/project/index");
        return
      }
      const params = {
        project_id: this.projectId,
        report_id: id,
        search: this.searchInput,
        page_num: 1,
        page_size: 30,
      };
      const { code, data } = await getReportDetailByError(params);
      if (code === 200) {
        let typeData = [{ type: "All" }];
        data.forEach((item) => {
          typeData.push({ type: item.rule_name });
        });
        this.list = data;
        this.total = data.length;
      }
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.page = 1;
      this.listQuery.limit = 30;
      this.getDataByErrorData();
    },
    // 详细弹窗展示对应数据
    handleClick(row) {
      this.$refs.detailDialogTableRef.openDialog(row);
    },
    // 分页器页码切换 获取页码和页数
    pageSwitch(val) {
      this.getDataByErrorData();
    },
  },
};
</script>

<style lang="scss" scoped>
.filter-type {
  display: flex;
  vertical-align: middle;
}

.input-with-select .el-input-group__prepend {
  background-color: #fff;
}

::v-deep .el-dialog__body {
  padding: 0 20px 20px 20px;
}

::v-deep .el-menu-item.is-active {
  background-color: #e8f4ff;
}

// ----------修改elementui表格的默认样式-----------
::v-deep .el-table__body-wrapper {
  // cursor: pointer;

  &::-webkit-scrollbar {
    height: 6px;
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    // 轨道颜色
    background-color: #fff;
  }

  &::-webkit-scrollbar-thumb {
    // 滚动块颜色
    background-color: #e6e9ed;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    // hover高亮
    background-color: #d5d8db;
  }
}

// 当表格没有滚动条时
::v-deep .el-table__body-wrapper.is-scrolling-none ~ .el-table__fixed-right {
  height: 100% !important;
  bottom: 0 !important;
  box-shadow: none !important;
}

// 当表格有纵向滚动条时
::v-deep .el-table--scrollable-y .el-table__fixed-right {
  right: 6px !important;
}

// 当表格只有横向滚动条，没有纵向滚动条时
::v-deep
  .el-table--scrollable-x:not(.el-table--scrollable-y)
  .el-table__fixed-right {
  right: 0 !important;
}
</style>
