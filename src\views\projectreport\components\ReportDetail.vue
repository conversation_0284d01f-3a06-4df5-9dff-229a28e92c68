<template>
  <div>
    <h4 style="margin-bottom: 0">{{ tableTitle }}</h4>
    <div style="width: 400px; display: flex; margin: 0 0 10px auto">
      <el-select
        v-model="ruleLevel"
        clearable
        placeholder="筛选报错等级"
        style="margin-right: 5px"
        @change="changeRuleLevel"
      >
        <el-option label="高" value="high" />
        <el-option label="中" value="middle" />
        <el-option label="低" value="low" />
      </el-select>
      <el-input
        :placeholder="$t('reportDetail.searchKeyWords')"
        v-model="searchInput"
        clearable
        @keyup.enter.native="handleFilter"
        @clear="handleFilter"
      >
        <el-button
          slot="append"
          icon="el-icon-search"
          @click="handleFilter"
        ></el-button>
      </el-input>
    </div>

    <el-container style="height: 800px; position: relative">
      <div
        style="
          width: 12.1vw;
          height: 44px;
          line-height: 30px;
          padding: 6px 0 6px 20px;
          background-color: #f5f7fa;
          position: absolute;
          border: 1px solid #dfe6ec;
          border-bottom: none;
        "
      >
        {{ $t("reportDetail.filterType") }}：
      </div>
      <el-scrollbar
        wrap-class="scrollbar-wrapper"
        wrap-style="overflow-x:hidden;"
        style="
          margin-top: 44px;
          border: 1px solid #dfe6ec;
          border-right: none;
          background-color: #f5f7fa;
        "
      >
        <el-aside width="12vw" style="padding: 0; margin-bottom: 0">
          <el-menu
            default-active="All"
            @select="handleSelect"
            style="background-color: #f5f7fa"
          >
            <el-menu-item
              v-for="(item, index) in reportTypeList"
              :key="index"
              :index="item.type"
              style="border-bottom: 1px solid #dfe6ec"
            >
              <div @mouseenter="handleShowTooltip($event)">
                <el-tooltip
                  :disabled="showTooltip"
                  :content="item.type"
                  placement="top"
                >
                  <div
                    style="
                      width: 100%;
                      overflow: hidden;
                      white-space: nowrap;
                      text-overflow: ellipsis;
                    "
                  >
                    {{ item.type }}
                  </div>
                </el-tooltip>
              </div>
            </el-menu-item>
          </el-menu>
        </el-aside>
      </el-scrollbar>
      <el-main style="padding: 0">
        <el-table :data="list" border max-height="800px">
          <!-- <el-table-column fixed prop="id" label="id" width="50" /> -->
          <el-table-column fixed prop="name" label="name" />
          <el-table-column prop="type" label="type" />
          <el-table-column prop="path" label="path" />
          <el-table-column
            prop="errorList"
            :label="$t('reportDetail.errorList')"
          >
            <!-- <template slot-scope="scope">
              {{ scope.row.ruleLevel + "# " + scope.row.errorList }}
            </template> -->
          </el-table-column>
          <el-table-column fixed="right" :label="$t('reportDetail.operation')">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="handleClick(scope.$index)"
                >{{ $t("reportDetail.viewMore") }}</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-main>
    </el-container>

    <!-- 分页器 -->
    <div style="display: flex; justify-content: end" v-if="total > 0">
      <Pagination
        style="margin-top: 10px; padding: 10px 20px"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="pageSwitch"
        :auto-scroll="listQuery.limit >= 30"
      />
    </div>
    <!-- 详情弹窗 -->
    <el-dialog
      v-el-drag-dialog
      :visible.sync="dialogTableVisible"
      :title="$t('reportDetail.detailedData')"
      width="60%"
    >
      <el-button type="text" @click="fieldName = !fieldName">{{
        $t("reportDetail.switchFieldName")
      }}</el-button>
      <el-descriptions :column="2">
        <el-descriptions-item
          v-for="(value, key) in fieldName ? cnDialogData : enDialogData"
          :key="key"
          :label-style="{ fontWeight: '600' }"
          :label="key"
          >{{ value }}</el-descriptions-item
        >
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import elDragDialog from "@/directive/el-drag-dialog";
import Pagination from "@/components/Pagination/index.vue";
import { getReportDetail } from "@/api/project";
import { getReportStat } from "@/api/project";
import { getRuleConfig } from "@/api/rule";

export default {
  name: "ReportDetail",
  components: { Pagination },
  directives: { elDragDialog },
  props: ["reportDetail", "tableTitle"],
  data() {
    return {
      ruleConfig: [], //保存规则分类

      reportTypeList: [],
      reportType: "",

      total: 0,
      list: [],
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 30,
      },
      cnDialogData: [], //保存中文字段名详细数据
      enDialogData: [], //保存英文字段名详细数据
      dialogTableVisible: false,
      selectType: "",
      searchInput: "", // 搜索关键词
      ruleLevel: "", //筛选报错规则等级
      fieldName: true, //详情弹窗的字段名语言切换 默认true中文
      showTooltip: false,
      
    };
  },

  mounted() {
    this.searchInput = this.$route.query.keyword;
    this.getRuleConfig();
    this.getReportStat();
  },
  methods: {
    // 筛选报错等级
    changeRuleLevel(val) {
      this.getList();
    },
    // 选择类型
    handleSelect(key) {
      this.reportType = key;
      this.listQuery.page = 1;
      this.listQuery.limit = 30;
      this.getList();
    },
    // 是否省略提示
    handleShowTooltip(event) {
      let cell = event.target.querySelector(".el-tooltip");
      this.showTooltip = cell.scrollWidth === cell.offsetWidth;
    },
    // 获取规则分类
    async getRuleConfig() {
      let params = {
        projectId: this.$route.query.projectId,
        branch: this.$route.query.branch,
      };
      let { code, json } = await getRuleConfig(params);
      if (code === 200) {
        this.ruleConfig = json;
      }
    },
    // 根据搜索类型和关键词过滤数据
    async getList() {
      let params = {
        projectId: this.$route.query.projectId,
        id: this.$route.query.reportId,
        pageNum: this.listQuery.page,
        pageSize: this.listQuery.limit,
        keyWord: this.searchInput,
        type: this.reportType !== "All" ? this.reportType : "",
        ruleLevel: this.ruleLevel !== "" ? this.ruleLevel : "",
      };
      let { code, data, dataLen, msg } = await getReportDetail(params);
      if (!data) return this.$message.error(msg);
      this.list = data;
      this.total = dataLen;
    },
    // 获取数据分类
    async getReportStat() {
      if (this.$route.query.branch) {
        let params = {
          projectId: this.$route.query.projectId,
          branch: this.$route.query.branch,
        };
        let { code, data } = await getReportStat(params);
        if (code === 200) {
          let typeData = [{ type: "All" }];
          data.stat.forEach((item) => {
            if (item.error_rule_num !== 0) typeData.push(item);
          });
          this.reportTypeList = typeData;
          this.reportType = "";
          this.getList();
        }
      }
    },
    // 筛选报告类型
    changeType() {
      this.listQuery.page = 1;
      this.listQuery.limit = 30;
      this.getList();
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.page = 1;
      this.listQuery.limit = 30;
      this.getList();
    },

    // 详细弹窗展示对应数据
    handleClick(i) {
      this.dialogTableVisible = true;
      this.enDialogData = this.list[i]; //保存英文字段弹窗展示数据
      let data = this.list[i];
      // 处理中文字段名称
      let stypeIndex = (this.ruleConfig || []).findIndex(
        (item) => item.type === data.type
      );
      let values = this.ruleConfig[stypeIndex].values;
      let cnName = {}; //保存中文名称key
      values.forEach((item) => {
        cnName[item.name] = item.cn ? item.cn : item.name;
      });
      cnName.scan_time = "扫描时间";
      cnName.errorList = "报错列表";
      cnName.type = "类型";
      cnName.branch = "分支";
      cnName.size = "大小";
      let objs = Object.keys(data).reduce((newData, key) => {
        let newKey = cnName[key] || key;
        newData[newKey] = data[key];
        return newData;
      }, {});
      this.cnDialogData = objs; //保存中文字段弹窗展示数据
    },

    // 分页器页码切换 获取页码和页数
    pageSwitch(val) {
      this.getList();
    },
  },
};
</script>

<style lang="scss" scoped>
.filter-type {
  display: flex;
  vertical-align: middle;
}

.input-with-select .el-input-group__prepend {
  background-color: #fff;
}

::v-deep .el-dialog__body {
  padding: 0 20px 20px 20px;
}

::v-deep .el-menu-item.is-active {
  background-color: #e8f4ff;
}

// ----------修改elementui表格的默认样式-----------
::v-deep .el-table__body-wrapper {
  // cursor: pointer;

  &::-webkit-scrollbar {
    height: 6px;
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    // 轨道颜色
    background-color: #fff;
  }

  &::-webkit-scrollbar-thumb {
    // 滚动块颜色
    background-color: #e6e9ed;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    // hover高亮
    background-color: #d5d8db;
  }
}

// 解决表格固定列问题
// ::v-deep .el-table__fixed,
// .el-table__fixed-right {
//   height: calc(100% - 7px) !important;
//   box-shadow: -5px -2px 10px rgba(0, 0, 0, 0.12) !important;
//   .el-table__fixed-body-wrapper {
//     height: calc(100% - 36px) !important;
//   }
// }
// 当表格没有滚动条时
::v-deep .el-table__body-wrapper.is-scrolling-none ~ .el-table__fixed-right {
  height: 100% !important;
  bottom: 0 !important;
  box-shadow: none !important;
}

// 当表格有纵向滚动条时
::v-deep .el-table--scrollable-y .el-table__fixed-right {
  right: 6px !important;
}

// 当表格只有横向滚动条，没有纵向滚动条时
::v-deep
  .el-table--scrollable-x:not(.el-table--scrollable-y)
  .el-table__fixed-right {
  right: 0 !important;
}
</style>
