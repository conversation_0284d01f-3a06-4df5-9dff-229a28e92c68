<template>
  <div class="app-container">
    <div v-if="user">
      <el-row :gutter="20">
        <el-col :span="8" :xs="24">
          <user-card :user="user" />
        </el-col>

        <el-col :span="16" :xs="24">
          <el-card>
            <el-tabs v-model="activeTab">
              <el-tab-pane
                :label="$t('profile.basicInformation')"
                name="account"
              >
                <account :user="accountUser" />
              </el-tab-pane>
              <el-tab-pane :label="$t('profile.accountBind')" name="activity">
                <activity :user="user" />
              </el-tab-pane>
            </el-tabs>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import UserCard from "./components/UserCard";
import Activity from "./components/Activity";
import Account from "./components/Account";
import { getInfo } from "@/api/user";

export default {
  name: "Profile",
  components: { UserCard, Activity, Account },
  data() {
    return {
      user: {},
      activeTab: "account",
      accountUser: {},
    };
  },
  computed: {
    ...mapGetters(["name", "avatar"]),
  },
  created() {
    this.getInfo();
  },
  methods: {
    async getInfo() {
      let { code, data } = await getInfo();
      if (code === 200) {
        this.user = {
          userId: data.userId,
          name: data.userName,
          role: data.roleName,
          nickName: data.nickName,
          phone: data.phone,
          email: data.email,
          avatar: `${process.env.VUE_APP_BASE_API}/media/avatar/${
            data.userId
          }.png?time=${Date.now()}`,
        };
        this.accountUser = {
          name: data.userName,
          nickName: data.nickName,
        };
      }
    },
  },
};
</script>
