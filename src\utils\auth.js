import Cookies from "js-cookie";

const TokenKey = "AssetsLint-Token";

export function getToken() {
  return Cookies.get(TokenKey);
  // return sessionStorage.getItem(TokenKey)
}

export function setToken(token) {
  return Cookies.set(To<PERSON><PERSON><PERSON>, token);
  // return sessionStorage.setItem(Token<PERSON><PERSON>, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey);
  // return sessionStorage.removeItem(TokenKey)
}

export function removeLocalStorage() {
  localStorage.removeItem("rule_projectId");
  localStorage.removeItem("rule_projectBranch");
  localStorage.removeItem("projectName");
  localStorage.removeItem("projectBranch");
  localStorage.removeItem("rule_project");
  localStorage.removeItem("rule_form");
  localStorage.removeItem("rule_table_form");
  localStorage.removeItem("rule_table_sheet");
  // 清除sessionStorage中的项目信息
  sessionStorage.removeItem("currentProjectId");
  sessionStorage.removeItem("currentProjectName");
  sessionStorage.removeItem("currentProjectBranch");
  return localStorage.clear();
}
