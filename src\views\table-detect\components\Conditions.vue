<template>
  <div>
    <div
      v-for="item in conditions"
      :key="item.key"
      class="group"
      :style="{
        border: item.type == 'group' ? '1px solid #999' : '',
      }"
    >
      <!-- 当条件类型为group时候 条件组相关的选择项渲染出来 -->
      <div v-if="item.type == 'group'">
        <div style="display: flex; align-items: center">
          <el-select
            v-if="item.type == 'group'"
            v-model="item.operator"
            name="operator"
            :placeholder="$t('ruleCreate.choose')"
            size="mini"
            style="width: 110px; margin-right: 5px"
          >
            <el-option label="and" value="and" />
            <el-option label="or" value="or" />
          </el-select>
          <!-- 当value存在并且是数组,递归子组件自身实现嵌套 -->
          <Conditions
            v-if="item.value && item.value.length >= 1"
            :conditions="item.value"
            :tableHeaders="tableHeaders"
            :operatorList="operatorList"
            :belongType="belongType"
            :key="item.key"
          />
        </div>

        <div>
          <el-button
            v-if="item.type == 'group' && item.value.length >= 0"
            icon="el-icon-circle-plus-outline"
            type="info"
            size="mini"
            @click="addConditionGroup(item.key)"
            >{{ $t("ruleCreate.addConditionGroup") }}</el-button
          >
          <el-button
            v-if="item.type == 'group' && item.value.length >= 0"
            icon="el-icon-plus"
            type="info"
            size="mini"
            style="margin-left: 5px"
            @click="addCondition(item.key)"
            >{{ $t("ruleCreate.addCondition") }}</el-button
          >

          <el-button
            v-if="item.type == 'group'"
            icon="el-icon-remove"
            type="info"
            size="mini"
            style="margin-left: 5px"
            @click="removeConditionGroup(item.key)"
            >{{ $t("ruleCreate.deleteConditionGroup") }}</el-button
          >
        </div>
      </div>
      <!-- 当条件的类型是meta执行渲染条件的各个填写选项 -->
      <div v-if="item.type == 'meta'">
        <!-- 表头选择器 -->
        <el-select
          v-model="item.tableHeader"
          name="tableHeader"
          :placeholder="'请选择表头'"
          size="mini"
          style="width: 200px; margin-right: 5px"
        >
          <el-option
            v-for="(v, index) in tableHeaders"
            :key="index"
            :label="'Col_' + index"
            :value="'Col_' + index"
          />
        </el-select>
        <!-- 规则类型对应操作符的选择器 -->
        <el-select
          v-model="item.operator"
          name="operator"
          placeholder="请选择运算符"
          size="mini"
          style="margin-right: 5px"
        >
          <div v-for="o in operatorList">
            <el-option
              v-if="o.belong_type.includes(belongType)"
              :key="o.id"
              :label="o.name_cn"
              :value="o.name_cn"
            />
          </div>
        </el-select>
        <el-autocomplete
          v-if="isShowInput(item.operator)"
          v-model="item.value"
          :fetch-suggestions="querySearch"
          size="mini"
          clearable
          placeholder="请选择或者填写目标值"
        ></el-autocomplete>

        <!-- <el-button
          type="info"
          circle
          icon="el-icon-plus"
          size="mini"
          style="margin-left: 10px"
          @click="addCondition(item.key)"
        /> -->
        <el-button
          type="info"
          circle
          icon="el-icon-minus"
          size="mini"
          style="margin-left: 10px"
          @click="removeCondition(item.key)"
        />

        <!-- <el-button
          type="info"
          icon="el-icon-plus"
          size="mini"
          @click="addConditionGroup(item.key)"
          >{{ $t("ruleCreate.insertConditionGroup") }}</el-button
        > -->
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
export default {
  name: "Conditions",
  props: {
    conditions: {
      default: function () {
        return [];
      },
    },
    tableHeaders: {
      default: function () {
        return [];
      },
    },
    operatorList: {
      default: function () {
        return [];
      },
    },
    belongType: {
      type: String,
      default: "",
    },
  },
  data() {
    return {};
  },
  computed: {
    ...mapGetters(["language"]),
  },
  mounted() {
    // el-input type=number时,如果点击键盘上下可以改变input中的值,这个方法是禁止改变
    document.onkeydown = function () {
      if (window.event.keyCode === 38 || window.event.keyCode === 40) {
        window.event.returnValue = false;
      }
    };
  },

  methods: {
    // 切换运算符保存值类型
    isShowInput(val) {
      if (!val) return true;
      const operatorIndex = this.operatorList.findIndex(
        (obj) => obj["name_cn"] === val
      );
      const expected_type = this.operatorList[operatorIndex].expected_type;
      if (expected_type !== null) return true;
      return false;
      // this.form.calc[index].expected_type =
      //   this.operatorList[operatorIndex].expected_type;
    },
    // 条件和目标参数的目标值表头选择项
    querySearch(queryString, cb) {
      let tableList = this.tableHeaders.map((item, index) => {
        return {
          value: "Col_" + index,
        };
      });
      let results = queryString
        ? tableList.filter(this.createFilter(queryString))
        : tableList;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    createFilter(queryString) {
      return (table) => {
        return (
          table.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1
        );
      };
    },

    // 获取操作符的具体值 发射对应自定义事件
    getOperatorValue(value, key) {
      // console.log(value);
      this.$store.dispatch("tableCondition/getOperatorValue", key);
    },
    // 获取操作符的类型 发射对应自定义事件
    getOperatorType(type, key) {
      let data = { type, key };
      this.$store.dispatch("tableCondition/getOperatorType", data);
    },

    // 添加条件的自定义事件
    addCondition(key) {
      this.$store.dispatch("tableCondition/addCondition", key);
    },
    // 删除条件的自定义事件
    removeCondition(key) {
      this.$store.dispatch("tableCondition/removeCondition", key);
    },
    // 添加条件组的自定义事件
    addConditionGroup(key) {
      this.$store.dispatch("tableCondition/addConditionGroup", key);
    },
    // 删除条件组的自定义事件
    removeConditionGroup(key) {
      this.$confirm(
        this.$t("ruleCreate.isDelConditionGroup"),
        this.$t("ruleCreate.tips"),
        {
          type: "warning",
        }
      )
        .then(() => {
          this.$message({
            type: "success",
            message: this.$t("ruleCreate.deleted"),
          });
          this.$store.dispatch("tableCondition/removeConditionGroup", key);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: this.$t("ruleCreate.cancelDelete"),
          });
        });
    },
    // 当没有数据时先创建初始条件组的自定义事件
    // addFirstConditionGroup(createType) {
    //   this.$store.commit("tableCondition/addFirstConditionGroup", createType);
    // },
  },
};
</script>

<style lang="scss" scoped>
.group {
  // display: flex;
  // align-items: center;
  // margin: 4px;
  padding: 0 5px;
  background-color: #edeff2;
  border-radius: 4px;
}

::v-deep.el-button--mini.is-circle {
  padding: 3px;
}

::v-deep.el-button--mini {
  padding: 6px 10px;
}

// 解决el-input设置类型为number时，去掉输入框后面上下箭头
::v-deep.el-input input::-webkit-outer-spin-button,
::v-deep.el-input input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

::v-deep.el-input input[type="number"] {
  -moz-appearance: textfield;
}

// 解决el-input设置类型为number时，中文输入法光标上移问题
::v-deep.el-input input {
  line-height: 1px !important;
}
</style>
