<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="`【xxx用户】管理的项目`"
      :visible.sync="setProjectModal"
      :close-on-click-modal="false"
      width="60%"
    >
      <div class="search">
        <div>项目名称：</div>
        <el-input
          v-model="searchKey"
          placeholder="请输入项目名称"
          clearable
          size="small"
          @keyup.enter.native="searchUser"
        />
        <el-button
          type="primary"
          icon="el-icon-search"
          size="small"
          @click="searchUser"
          >查询</el-button
        >
      </div>
      <el-button type="primary" icon="el-icon-plus" @click="handleAddUser"
        >已有项目</el-button
      >
      <el-popconfirm
        title="前往用户管理？"
        placement="top-start"
        @confirm="goCreateProject"
      >
        <el-button slot="reference" type="primary" style="margin-left: 15px"
          >创建新项目</el-button
        >
      </el-popconfirm>
      <el-dropdown v-if="projectIds.length > 0" @command="removeSelectProject">
        <el-button style="margin-left: 15px">
          批量操作<i class="el-icon-arrow-down el-icon--right" />
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item
            icon="el-icon-magic-stick"
            command="removeSelectProject"
            >取消关联</el-dropdown-item
          >
        </el-dropdown-menu>
      </el-dropdown>
      <el-table
        ref="multipleTable"
        :data="useProjectlist"
        border
        tooltip-effect="dark"
        style="width: 100%; margin-top: 20px"
        max-height="330"
        :header-cell-style="{
          background: '#F7FBFF',
          height: '52px',
        }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <!-- <el-table-column prop="projectName" label="项目Logo" /> -->
        <el-table-column prop="projectName" label="项目名称" />
        <el-table-column prop="description" label="项目描述" />
        <el-table-column label="操作">
          <!-- <el-button type="text" icon="el-icon-edit-outline">编辑</el-button> -->
          <!-- <el-button type="text" icon="el-icon-magic-stick">取消关联</el-button> -->
          <template slot-scope="scope">
            <el-popconfirm
              title="确定取消关联？"
              placement="top-start"
              @confirm="removeProject(scope.row)"
            >
              <el-button slot="reference" type="text" icon="el-icon-magic-stick"
                >取消关联</el-button
              >
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeUserSetDialog">取 消</el-button>
        <el-button type="primary" @click="closeUserSetDialog">确 认</el-button>
      </div>
    </el-dialog>
    <el-dialog
      width="65%"
      title="添加已有项目"
      :visible.sync="innerVisible"
      append-to-body
    >
      <div class="search">
        <div>用户名：</div>
        <el-input
          v-model="searchKey"
          placeholder="请输入项目名称"
          clearable
          size="small"
          @keyup.enter.native="searchUser"
        />
        <el-button
          type="primary"
          icon="el-icon-search"
          size="small"
          @click="searchUser"
          >查询</el-button
        >
      </div>
      <el-table
        ref="multipleTable"
        :data="useProjectlist"
        border
        tooltip-effect="dark"
        style="width: 100%; margin-top: 20px"
        max-height="400"
        :header-cell-style="{
          background: '#F7FBFF',
        }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="projectName" label="项目名称" />
        <el-table-column prop="description" label="项目描述" />
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="innerVisible = false">关 闭</el-button>
        <el-button type="primary" @click="confirmUserProject"
          >确认添加</el-button
        >
      </div>
      <pagination
        v-show="total > 10"
        style="margin-top: 0; padding: 10px 20px"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="pageSwitch"
      />
    </el-dialog>
  </div>
</template>

<script>
import elDragDialog from "@/directive/el-drag-dialog";
import Pagination from "@/components/Pagination/index.vue";
export default {
  directives: { elDragDialog },
  components: { Pagination },
  props: ["setProjectModal", "useProjectlist"],
  data() {
    return {
      searchKey: "", // 搜索关键词
      innerVisible: false,
      projectIds: [],
      total: 0,
      listQuery: {
        page: 1,
        limit: 10,
      },
    };
  },
  methods: {
    // 获取项目所属的用户
    getProjectUser() {
      // 传项目id获取
    },
    // 分页器页码改变获取数据
    pageSwitch(value) {
      // console.log(value.page, value.limit);
    },

    searchUser() {
      // 传参项目id和用户名发起请求
      console.log(this.searchKey);
    },
    // 获取选中的用户
    getSelectUser() {},
    confirmUserProject() {},
    // 调用父组件关闭弹窗方法
    closeUserSetDialog() {
      this.$parent.closeUserSetDialog();
    },
    handleAddUser() {
      this.innerVisible = true;
    },
    // 前往创建新用户
    goCreateProject() {
      this.$router.push("/permission/project");
    },

    handleSelectionChange(val) {
      this.projectIds = [];
      // console.log(val);
      val.forEach((item) => {
        this.projectIds.push(item.userId);
      });
      // 传选中的用户id(支持多个)和项目id发起移除关联请求
      console.log(this.projectIds.join());
    },
    // 取消关联用户
    removeProject(row) {
      // 传用户id和项目id删除关联的项目请求
      console.log(row.userId);
    },
    removeSelectProject() {
      // 通过项目id和选择的用户list移除关联项目
      console.log("取消选择关联用户");
    },
  },
};
</script>

<style lang="scss" scoped>
.search {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  .el-input {
    width: 300px;
    margin-right: 15px;
  }
}
</style>
