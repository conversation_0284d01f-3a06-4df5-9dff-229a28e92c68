<template>
  <div>
    <div
      v-for="item in conditions"
      :key="item.key"
      class="group"
      :style="{
        border: item.type == 'group' ? '1px solid #999' : '',
      }"
    >
      <!-- 当条件类型为group时候 条件组相关的选择项渲染出来 -->
      <div v-if="item.type == 'group'">
        <div style="display: flex; align-items: center">
          <el-select
            v-if="item.type == 'group'"
            v-model="item.operator"
            name="operator"
            :placeholder="$t('ruleCreate.choose')"
            size="mini"
            style="width: 110px; margin-right: 5px"
          >
            <el-option label="and" value="and" />
            <el-option label="or" value="or" />
            <el-option label="not and" value="not and" />
            <el-option label="not or" value="not or" />
          </el-select>
          <!-- 当value存在并且是数组,递归子组件自身实现嵌套 -->
          <conditions
            v-if="item.value && item.value.length >= 1"
            :conditions="item.value"
            :key="item.key"
          />
        </div>

        <div>
          <el-button
            v-if="item.type == 'group' && item.value.length >= 0"
            icon="el-icon-circle-plus-outline"
            type="info"
            size="mini"
            style="margin-left: 5px"
            @click="addConditionGroup(item.key)"
            >{{ $t("ruleCreate.addConditionGroup") }}</el-button
          >
          <el-button
            v-if="item.type == 'group' && item.value.length >= 0"
            icon="el-icon-plus"
            type="info"
            size="mini"
            @click="addCondition(item.key)"
            >{{ $t("ruleCreate.addCondition") }}</el-button
          >

          <el-button
            v-if="item.type == 'group'"
            icon="el-icon-remove"
            size="mini"
            style="margin-left: 5px; color: #f56c6c"
            @click="removeConditionGroup(item.key)"
            >{{ $t("ruleCreate.deleteConditionGroup") }}</el-button
          >
        </div>
      </div>
      <!-- 当条件的类型是meta执行渲染条件的各个填写选项 -->
      <div v-if="item.type == 'meta'">
        <el-select
          v-model="item.prefix"
          name="prefix"
          :placeholder="$t('ruleCreate.choose')"
          size="mini"
          style="width: 100px"
        >
          <el-option :label="$t('ruleCreate.forward')" value="true" />
          <el-option :label="$t('ruleCreate.reverse')" value="false" />
        </el-select>
        <!-- 规则类型选择器 -->
        <el-select
          v-model="item.field"
          name="field"
          :placeholder="$t('ruleCreate.choose')"
          size="mini"
          style="width: 200px"
        >
          <el-option
            v-for="(v, index) in ruleConfig[stypeIndex].values"
            :key="index"
            :label="language === 'zh' ? v.cn : v.name"
            :value="v.name"
            @click.capture.native="getOperatorType(v.type, item.key)"
          />
        </el-select>
        <!-- 规则类型对应操作符的选择器 -->
        <el-select
          v-model="item.operator"
          name="operator"
          :placeholder="$t('ruleCreate.choose')"
          size="mini"
          style="margin-right: 5px"
          @change="getOperatorValue($event, item.key)"
        >
          <el-option v-if="item.operatorType === 0" label=">" value=">" />
          <el-option v-if="item.operatorType === 0" label="<" value="<" />
          <el-option v-if="item.operatorType === 0" label=">=" value=">=" />
          <el-option v-if="item.operatorType === 0" label="<=" value="<=" />
          <el-option
            v-if="
              item.operatorType === 0 ||
              item.operatorType === 1 ||
              item.operatorType === 2
            "
            label="=="
            value="=="
          />
          <el-option
            v-if="item.operatorType === 0 || item.operatorType === 1"
            label="!="
            value="!="
          />
          <el-option
            v-if="item.operatorType === 1"
            :label="$t('ruleCreate.SatisfiesRegularity')"
            value="reg"
          />
          <el-option
            v-if="item.operatorType === 1"
            :label="$t('ruleCreate.include')"
            value="include"
          />
          <!-- 修改前：range 支持数值和字符串，in 只支持数值 -->
          <!-- <el-option
            v-if="item.operatorType === 0 || item.operatorType === 1"
            :label="$t('ruleCreate.withinRange')"
            value="range"
          />
          <el-option
            v-if="item.operatorType === 0"
            :label="$t('ruleCreate.inSet')"
            value="in"
          /> -->
          <!-- 修改后：range 只支持数值，in 支持数值和字符串 -->
          <el-option
            v-if="item.operatorType === 0"
            :label="$t('ruleCreate.withinRange')"
            value="range"
          />
          <el-option
            v-if="item.operatorType === 0 || item.operatorType === 1"
            :label="$t('ruleCreate.inSet')"
            value="in"
          />
        </el-select>

        <!-- 判断操作符的类型和操作符的值 渲染不同的输入框 -->
        <template
          v-if="
            item.operatorType === 0 &&
            item.operator !== 'in' &&
            item.operator !== 'range'
          "
        >
          <el-input
            v-model.number="item.value"
            name="value"
            style="width: 250px"
            size="mini"
            :placeholder="$t('ruleCreate.enterNumber')"
            type="number"
            onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)))"
          />
        </template>
        <!-- 修改前：in 操作符根据类型限制输入 -->
        <!-- <template v-if="item.operatorType === 0 && item.operator === 'in'">
          <el-input
            v-model.number="item.value"
            name="value"
            style="width: 250px"
            size="mini"
            :placeholder="$t('ruleCreate.enterNumber')"
            type="number"
            onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)))"
          />
        </template> -->
        <!-- 修改后：in 操作符支持逗号分隔的多个值 -->
        <template v-if="item.operator === 'in'">
          <el-input
            v-model="item.value"
            name="value"
            style="width: 250px"
            size="mini"
            :placeholder="$t('ruleCreate.enterCommaSeparatedValues')"
          />
        </template>
        <template v-if="item.operatorType === 0 && item.operator === 'range' && Array.isArray(item.value)">
          <el-input
            v-model.number="item.value[0]"
            name="value"
            style="width: 120px"
            size="mini"
            :placeholder="$t('ruleCreate.enterNumber')"
            type="number"
            onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)))"
          />
          <span> - </span>
          <el-input
            v-model.number="item.value[1]"
            name="value"
            style="width: 120px"
            size="mini"
            :placeholder="$t('ruleCreate.enterNumber')"
            type="number"
            onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)))"
          />
        </template>
        <template v-if="item.operatorType === 1&&item.operator!=='in'">
          <el-input
            v-model="item.value"
            name="value"
            style="width: 250px"
            size="mini"
            :placeholder="$t('ruleCreate.enterCharacters')"
          />
        </template>
        <el-select
          v-if="item.operatorType === 2"
          v-model="item.value"
          name="value"
          style="width: 250px"
          size="mini"
        >
          <el-option label="True" :value="true" />
          <el-option label="False" :value="false" />
        </el-select>

        <el-button
          type="info"
          circle
          icon="el-icon-plus"
          size="mini"
          style="margin-left: 10px"
          @click="addCondition(item.key)"
        />
        <el-button
          type="info"
          circle
          icon="el-icon-minus"
          size="mini"
          style="margin-left: 2px"
          @click="removeCondition(item.key)"
        />

        <el-button
          type="info"
          icon="el-icon-plus"
          size="mini"
          @click="addConditionGroup(item.key)"
          >{{ $t("ruleCreate.insertConditionGroup") }}</el-button
        >
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
// import ruleConfig from "@/assets/rule_config.json";
export default {
  name: "Conditions",
  props: {
    conditions: {
      default: function () {
        return [];
      },
    },
  },
  data() {
    return {
      // ruleConfig:[],
      // stypeIndex: "",
    };
  },
  computed: {
    ...mapGetters(["language", "ruleConfig", "stypeIndex"]),
  },
  mounted() {
    // el-input type=number时,如果点击键盘上下可以改变input中的值,这个方法是禁止改变
    document.onkeydown = function () {
      if (window.event.keyCode === 38 || window.event.keyCode === 40) {
        window.event.returnValue = false;
      }
    };
  },

  methods: {
    // 获取操作符的具体值 发射对应自定义事件
    getOperatorValue(value, key) {
      this.$store.dispatch("conditions/getOperatorValue", key);
    },
    // 获取操作符的类型 发射对应自定义事件
    getOperatorType(type, key) {
      let data = { type, key };
      this.$store.dispatch("conditions/getOperatorType", data);
    },

    // 添加条件的自定义事件
    addCondition(key) {
      this.$store.dispatch("conditions/addCondition", key);
    },
    // 删除条件的自定义事件
    removeCondition(key) {
      this.$confirm(
        this.$t("ruleCreate.isDelCondition"),
        this.$t("ruleCreate.tips"),
        {
          type: "warning",
        }
      )
        .then(() => {
          this.$message({
            type: "success",
            message: this.$t("ruleCreate.deleted"),
          });
          this.$store.dispatch("conditions/removeCondition", key);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: this.$t("ruleCreate.cancelDelete"),
          });
        });
    },
    // 添加条件组的自定义事件
    addConditionGroup(key) {
      this.$store.dispatch("conditions/addConditionGroup", key);
    },
    // 删除条件组的自定义事件
    removeConditionGroup(key) {
      this.$confirm(
        this.$t("ruleCreate.isDelConditionGroup"),
        this.$t("ruleCreate.tips"),
        {
          type: "warning",
        }
      )
        .then(() => {
          this.$message({
            type: "success",
            message: this.$t("ruleCreate.deleted"),
          });
          this.$store.dispatch("conditions/removeConditionGroup", key);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: this.$t("ruleCreate.cancelDelete"),
          });
        });
    },
    // 当没有数据时先创建初始条件组的自定义事件
    // addFirstConditionGroup(createType) {
    //   this.$store.commit("conditions/addFirstConditionGroup", createType);
    // },
  },
};
</script>

<style lang="scss" scoped>
.group {
  // display: flex;
  // align-items: center;
  // margin: 4px;
  padding-left: 4px;
  background-color: #edeff2;
  border-radius: 4px;
}

::v-deep.el-button--mini.is-circle {
  padding: 3px;
}

::v-deep.el-button--mini {
  padding: 6px 10px;
}

// 解决el-input设置类型为number时，去掉输入框后面上下箭头
::v-deep.el-input input::-webkit-outer-spin-button,
::v-deep.el-input input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

::v-deep.el-input input[type="number"] {
  -moz-appearance: textfield;
}

// 解决el-input设置类型为number时，中文输入法光标上移问题
::v-deep.el-input input {
  line-height: 1px !important;
}
</style>
