<template>
  <div class="app-container">
    <div class="project-list">
      <div class="el-page-header">
        <div class="el-page-header__content">{{ $t("project.myProject") }}</div>
      </div>
      <el-divider />
      <el-row v-if="userProjectList.length > 0" :gutter="20">
        <el-col v-for="(g, i) in userProjectList" :key="i" :span="spanNum">
          <div style="margin-bottom: 20px" @click="selectCard(i)">
            <el-card
              :shadow="i === selectProjectIndex ? 'always' : 'hover'"
              style="cursor: pointer"
            >
              <div style="display: flex">
                <img
                  :src="`${baseUrl}/media/sculpture/${
                    g.projectId
                  }.png?time=${Date.now()}`"
                  alt=""
                  style="width: 100px; height: 100px; margin-right: 15px"
                />
                <div>
                  <span style="font-weight: 600">{{ g.projectName }}</span>
                  <div
                    style="
                      height: 65px;
                      margin-top: 15px;
                      font-size: 14px;
                      display: flex;
                      flex-direction: column;
                      justify-content: space-between;
                    "
                  >
                    <span
                      >{{ $t("project.latestVersion") }}:
                      {{
                        g.up_to_date.branch
                          ? g.up_to_date.branch
                          : $t("project.noData")
                      }}</span
                    >
                    <span
                      >{{ $t("project.errorCount") }}:
                      {{
                        g.up_to_date.error_rule_num > 0
                          ? g.up_to_date.error_rule_num
                          : $t("project.noData")
                      }}</span
                    >
                    <span
                      >{{ $t("project.lastScanned") }}:
                      {{
                        g.up_to_date.scan_time
                          ? g.up_to_date.scan_time
                          : $t("project.noData")
                      }}</span
                    >
                  </div>
                </div>
              </div>
            </el-card>
          </div>
        </el-col>
      </el-row>
      <!-- 无数据时显示空状态 -->
      <el-empty v-else :image-size="50"></el-empty>
    </div>
    <div
      v-if="userProjectList.length > 0"
      style="margin-top: 20px; padding: 20px; background-color: #fff"
    >
      <AllVersions
        :projectData="projectData"
        :branchsData="branchsData"
        :spanNum="spanNum"
      />
    </div>
    <!-- 返回顶部组件 -->
    <el-tooltip placement="top" :content="$t('backToTop')">
      <back-to-top
        :visibility-height="300"
        :back-position="50"
        transition-name="fade"
      ></back-to-top>
    </el-tooltip>
  </div>
</template>

<script>
import AllVersions from "./components/AllVersions.vue";
import { getProjectBranchs, getUserProjectList } from "@/api/project";
import BackToTop from "@/components/BackToTop";

export default {
  components: { AllVersions, BackToTop },
  data() {
    return {
      windowWidth: document.documentElement.clientWidth, //实时屏幕宽度
      isShadow: "hover",
      spanNum: 6,

      userProjectList: [],
      baseUrl: process.env.VUE_APP_BASE_API, //图片基础路径
      selectProjectIndex: 0, // 选择对应项目卡片
      projectData: "",
      branchsData: [],
    };
  },
  computed: {
    projectCardIndex() {
      return this.$store.getters.projectCardIndex;
    },
  },
  watch: {
    // <!--在watch中监听实时宽度-->
    windowWidth(val) {
      // console.log("实时屏幕宽度：", val);
      if (val >= 1352 && val < 1694) {
        this.spanNum = 8;
      } else if (val >= 690 && val < 1352) {
        this.spanNum = 12;
      } else if (val < 690) {
        this.spanNum = 20;
      } else {
        this.spanNum = 6;
      }
    },
  },
  mounted() {
    // 获取用户所属项目
    this.getUserProjectList(this.projectCardIndex);
    this.setSpanNum(this.windowWidth);
    // <!--把window.onresize事件挂在到mounted函数上-->
    window.onresize = () => {
      return (() => {
        window.fullWidth = document.documentElement.clientWidth;
        this.windowWidth = window.fullWidth; // 宽
      })();
    };
  },
  methods: {
    // 获取所有项目
    async getUserProjectList(index) {
      let { code, projectList, dataLen } = await getUserProjectList();
      if (code === 200 && projectList.length > 0) {
        this.userProjectList = projectList;
        // 进入/刷新获取选择的项目
        this.selectCard(index);
      }
    },

    // 初次进入此页面的宽度设置卡片大小
    setSpanNum(width) {
      if (width >= 1352 && width < 1694) {
        this.spanNum = 8;
      } else if (width >= 690 && width < 1352) {
        this.spanNum = 12;
      } else if (width < 690) {
        this.spanNum = 20;
      } else {
        this.spanNum = 6;
      }
    },
    // 切换对应项目
    selectCard(i) {
      this.$store.commit("project/setProjectCardIndex", i); //记住选中的项目选项
      this.projectData = this.userProjectList[i];
      this.selectProjectIndex = i;
      this.getProjectBranchs(this.projectData.projectId);
    },

    // 获取项目所有报告
    async getProjectBranchs(projectId) {
      let { code, data } = await getProjectBranchs({ projectId });
      if (code === 200) {
        this.branchsData = data;
        this.branchsData.push({});
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  .project-list {
    background-color: #fff;
    padding: 20px;
  }
}
::v-deep .el-card.is-always-shadow {
  box-shadow: 0 2px 12px 0 rgb(0 0 0 /40%);
}
::v-deep .el-card__body {
  padding: 12px;
}
</style>
