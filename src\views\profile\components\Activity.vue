<template>
  <div>
    <div class="user-addcount">
      <ul>
        <li>
          <p class="title">{{ $t("profile.bindPhone") }}</p>
          <p class="desc">
            {{ $t("profile.boundPhone") }}：{{ user.phone }}
            <a href="javascript:void(0)" @click="changePhoneFlag = true">{{
              $t("profile.modify")
            }}</a>
          </p>
        </li>
        <el-divider></el-divider>
        <li>
          <p class="title">{{ $t("profile.bindEmail") }}</p>
          <p class="desc">
            {{ $t("profile.boundEmail") }}：{{ user.email }}
            <a href="javascript:void(0)" @click="changeEmailFlag = true">{{
              $t("profile.modify")
            }}</a>
          </p>
        </li>
        <el-divider></el-divider>
        <!-- <li>
        <p class="title">密保问题</p>
        <p class="desc">
          未设置密保问题
          <a href="javascript:void(0)">去设置</a>
        </p>
      </li>
      <el-divider></el-divider> -->
        <li>
          <p class="title">{{ $t("profile.modifyPassword") }}</p>
          <p class="desc">
            {{ $t("profile.modifyLoginPassword") }}
            <a href="javascript:void(0)" @click="showPassword = true">{{
              $t("profile.modifyPassword")
            }}</a>
          </p>
        </li>
        <el-divider></el-divider>
      </ul>
    </div>
    <!-- 修改密码弹窗 -->
    <el-dialog
      :visible.sync="showPassword"
      :title="$t('profile.modifyPassword')"
      :close-on-click-modal="false"
      width="420px"
      @close="clearPassword"
    >
      <el-form
        ref="modifyPwdForm"
        :model="pwdModify"
        :rules="rules"
        label-width="148px"
      >
        <el-form-item
          :minlength="6"
          :label="$t('profile.password')"
          prop="password"
        >
          <el-input v-model="pwdModify.password" show-password />
        </el-form-item>
        <el-form-item
          :minlength="6"
          :label="$t('profile.newPassword')"
          prop="newPassword"
        >
          <el-input v-model="pwdModify.newPassword" show-password />
        </el-form-item>
        <el-form-item
          :minlength="6"
          :label="$t('profile.confirmPassword')"
          prop="confirmPassword"
        >
          <el-input v-model="pwdModify.confirmPassword" show-password />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="showPassword = false">{{
            $t("profile.cancel")
          }}</el-button>
          <el-button size="small" type="primary" @click="savePassword">{{
            $t("profile.ok")
          }}</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 修改手机号码弹窗 -->
    <el-dialog
      :visible.sync="changePhoneFlag"
      :title="$t('profile.modifyPhone')"
      :close-on-click-modal="false"
      width="360px"
      @close="closeChangePhone"
    >
      <el-form
        ref="modifyPhoneForm"
        :model="phoneForm"
        :rules="rules"
        label-width="65px"
      >
        <el-form-item :label="$t('profile.phone')" prop="phone">
          <el-input
            v-model="phoneForm.phone"
            :placeholder="$t('profile.enterPhoneNum')"
            autocomplete="off"
          />
        </el-form-item>
        <!-- <el-form-item label="验证码" label-width="120px">
          <div class="code-box">
            <el-input
              v-model="phoneForm.code"
              autocomplete="off"
              placeholder="请自行设计短信服务，此处为模拟随便写"
              style="width: 300px"
            />
            <el-button
              size="small"
              type="primary"
              :disabled="time > 0"
              @click="getCode"
              >{{ time > 0 ? `(${time}s)后重新获取` : "获取验证码" }}</el-button
            >
          </div>
        </el-form-item> -->
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button size="small" @click="changePhoneFlag = false">{{
            $t("profile.cancel")
          }}</el-button>
          <el-button type="primary" size="small" @click="changePhone">{{
            $t("profile.change")
          }}</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog
      :visible.sync="changeEmailFlag"
      :title="$t('profile.bindEmail')"
      :close-on-click-modal="false"
      width="360px"
      @close="closeChangeEmail"
    >
      <el-form
        ref="modifyEmailForm"
        :model="emailForm"
        :rules="rules"
        label-width="60px"
      >
        <el-form-item :label="$t('profile.email')" prop="email">
          <el-input
            v-model="emailForm.email"
            :placeholder="$t('profile.enterEmail')"
            autocomplete="off"
          />
        </el-form-item>
        <!-- <el-form-item label="验证码" label-width="120px">
          <div class="code-box">
            <el-input
              v-model="emailForm.code"
              placeholder="请自行设计邮件服务，此处为模拟随便写"
              autocomplete="off"
              style="width: 300px"
            />
            <el-button
              size="small"
              type="primary"
              :disabled="emailTime > 0"
              @click="getEmailCode"
              >{{
                emailTime > 0 ? `(${emailTime}s)后重新获取` : "获取验证码"
              }}</el-button
            >
          </div>
        </el-form-item> -->
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button size="small" @click="changeEmailFlag = false">{{
            $t("profile.cancel")
          }}</el-button>
          <el-button type="primary" size="small" @click="changeEmail">{{
            $t("profile.change")
          }}</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { updateUserInfo, updatePwd } from "@/api/user";
import { setToken } from "@/utils/auth";

export default {
  props: {
    user: {
      type: Object,
      default: () => {
        return {
          phone: "",
          email: "",
        };
      },
    },
  },
  data() {
    return {
      changePhoneFlag: false, //修改手机弹窗
      changeEmailFlag: false, //修改邮箱弹窗
      showPassword: false, //修改密码弹窗

      // 保存手机号码
      phoneForm: {
        phone: "",
        code: "",
      },
      // 保存邮箱数据
      emailForm: {
        email: "",
        code: "",
      },
      //密码数据保存
      pwdModify: {
        password: "",
        newPassword: "",
        confirmPassword: "",
      },

      rules: {
        phone: [
          {
            required: true,
            message: this.$t("profile.enterPhoneNum"),
            trigger: "blur",
          },
          {
            pattern: /^((\+|00)86)?1\d{10}$/,
            message: this.$t("permissionUser.phoneRegTips"),
            trigger: "blur",
          },
        ],
        email: [
          {
            required: true,
            message: this.$t("profile.enterEmail"),
            trigger: "blur",
          },
          {
            pattern: /^\w{3,}(\.\w+)*@[A-z0-9]+(\.[A-z]{2,5}){1,2}$/,
            message: this.$t("permissionUser.emailRegTips"),
            trigger: "blur",
          },
        ],
        password: [
          {
            required: true,
            message: this.$t("profile.password"),
            trigger: "blur",
          },
          {
            min: 6,
            message: this.$t("profile.passwordMinCharacters"),
            trigger: "blur",
          },
        ],
        newPassword: [
          {
            required: true,
            message: this.$t("profile.newPassword"),
            trigger: "blur",
          },
          {
            min: 6,
            message: this.$t("profile.passwordMinCharacters"),
            trigger: "blur",
          },
        ],
        confirmPassword: [
          {
            required: true,
            message: this.$t("profile.confirmPassword"),
            trigger: "blur",
          },
          {
            min: 6,
            message: this.$t("profile.passwordMinCharacters"),
            trigger: "blur",
          },
          {
            validator: (rule, value, callback) => {
              if (value !== this.pwdModify.newPassword) {
                callback(
                  new Error(this.$t("profile.twoPasswordsInconsistent"))
                );
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {
    // 关闭修改手机号
    closeChangePhone() {
      this.phoneForm = {
        phone: "",
        code: "",
      };
      this.$refs.modifyPhoneForm.clearValidate();
    },
    //提交更改手机号
    async changePhone() {
      // 向后台请求更改手机号
      this.$refs.modifyPhoneForm.validate(async (valid) => {
        if (valid) {
          let { code, data } = await updateUserInfo({
            phone: this.phoneForm.phone,
          });
          if (code === 200) {
            setToken(data.token);
            if (code === 200) {
              this.$message.success(this.$t("profile.changeSuccessful"));
              this.changePhoneFlag = false;
            }
          }
        }
      });
    },
    async getCode() {
      let time = 60;
      let timer = setInterval(() => {
        time--;
        if (time <= 0) {
          clearInterval(timer);
          timer = null;
        }
      }, 1000);
    },
    // 关闭修改邮箱
    closeChangeEmail() {
      this.emailForm = {
        email: "",
        code: "",
      };
      this.$refs.modifyEmailForm.clearValidate();
    },
    // 修改邮箱
    async changeEmail() {
      // 向后台发送修改邮箱
      this.$refs.modifyEmailForm.validate(async (valid) => {
        if (valid) {
          let { code, data } = await updateUserInfo({
            email: this.emailForm.email,
          });
          if (code === 200) {
            setToken(data.token);
            if (code === 200) {
              this.$message.success(this.$t("profile.changeSuccessful"));
              this.changeEmailFlag = false;
            }
          }
        }
      });
    },
    async getEmailCode() {
      let emailTime = 60;
      let timer = setInterval(() => {
        emailTime--;
        if (emailTime <= 0) {
          clearInterval(timer);
          timer = null;
        }
      }, 1000);
    },

    //取消密码修改
    clearPassword() {
      this.pwdModify = {
        password: "",
        newPassword: "",
        confirmPassword: "",
      };
      this.$refs.modifyPwdForm.clearValidate();
    },
    //保存密码修改
    async savePassword() {
      this.$refs.modifyPwdForm.validate(async (valid) => {
        if (valid) {
          let params = {
            old_password: this.pwdModify.password,
            new_password: this.pwdModify.newPassword,
          };
          let { code, msg } = await updatePwd(params);
          if (code === 200) {
            this.$message.success(this.$t("profile.changeSuccessful"));
            this.showPassword = false;
          }
        } else {
          return false;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.user-addcount {
  // background-color: #fff;
  // padding: 20px;
  // border-radius: 8px;
  ul {
    list-style: none;
    padding-inline-start: 0;
    li {
      .title {
        // padding: 10px;
        font-size: 14px;
        font-weight: 700;
        color: #606266;
      }
      .desc {
        font-size: 14px;
        // padding: 0 10px 20px 10px;
        color: #a9a9a9;
        a {
          color: rgb(64, 158, 255);
          float: right;
        }
      }
      // border-bottom: 2px solid #f0f2f5;
      &:last-child {
        border-bottom: none;
      }
    }
  }
}
</style>
