<template>
  <el-breadcrumb class="app-breadcrumb" separator="/">
    <transition-group name="breadcrumb">
      <el-breadcrumb-item v-for="(item, index) in levelList" :key="item.path">
        <span v-if="item.redirect === 'noRedirect' || index == levelList.length - 1" class="no-redirect">{{
          generateTitle(item.meta.title) === "项目报告"
            ? `${projectName}: ${projectBranch} 报告`
            : generateTitle(item.meta.title)
        }}</span>
        <a v-else @click.prevent="handleLink(item)">{{
          generateTitle(item.meta.title) === "项目报告"
            ? `${projectName}: ${projectBranch} 报告`
            : generateTitle(item.meta.title)
        }}</a>
      </el-breadcrumb-item>
    </transition-group>
  </el-breadcrumb>
</template>

<script>
import { generateTitle } from '@/utils/i18n'
import pathToRegexp from 'path-to-regexp'
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      levelList: null
    }
  },
  watch: {
    $route(route) {
      // if you go to the redirect page, do not update the breadcrumbs
      if (route.path.startsWith('/redirect/')) {
        return
      }
      this.getBreadcrumb()
    }
  },
  created() {
    // 在页面加载时读取localStorage里的状态信息
    if (
      localStorage.getItem('projectName') &&
      localStorage.getItem('projectBranch')
    ) {
      this.$store.commit(
        'project/setProjectName',
        localStorage.getItem('projectName')
      )
      this.$store.commit(
        'project/setProjectBranch',
        localStorage.getItem('projectBranch')
      )

      localStorage.removeItem('projectName')
      localStorage.removeItem('projectBranch')
    }
    // 在页面刷新时将vuex里的信息保存到localStorage里
    window.addEventListener('beforeunload', () => {
      localStorage.setItem('projectName', this.projectName)
      localStorage.setItem('projectBranch', this.projectBranch)
    })

    this.getBreadcrumb()
  },
  computed: {
    ...mapGetters(['projectName', 'projectBranch'])
  },
  methods: {
    generateTitle,
    getBreadcrumb() {
      // only show routes with meta.title
      let matched = this.$route.matched.filter(
        (item) => item.meta && item.meta.title
      )
      const first = matched[0]

      if (!this.isHome(first)) {
        // matched = [{ path: "/home", meta: { title: "home" } }].concat(matched);
        // 无首页处理
        matched = [{}].concat(matched)
      }

      if (matched.length > 1) {
        const insertPosition = matched.length - 1
        let parent = matched[insertPosition].meta.parent
        while (parent) {
          const route = this.$router.resolve(parent).route
          matched.splice(insertPosition, 0, route)
          parent = route.meta.parent
        }
      }

      this.levelList = matched.filter(
        (item) => item.meta && item.meta.title && item.meta.breadcrumb !== false
      )
    },
    isHome(route) {
      const name = route && route.name
      if (!name) {
        return false
      }
      return name.trim().toLocaleLowerCase() === 'Home'.toLocaleLowerCase()
    },
    pathCompile(path) {
      const { params } = this.$route
      var toPath = pathToRegexp.compile(path)
      return toPath(params)
    },
    handleLink(item) {
      const { redirect, path } = item

      if (redirect) {
        this.$router.push(redirect)
        return
      }
      if (path === '/prodetail/report') {
        this.$router.back()
        return
      }

      this.$router.push(this.pathCompile(path))
    }
  }
}
</script>

<style lang="scss" scoped>
.app-breadcrumb.el-breadcrumb {
  display: inline-block;
  font-size: 14px;
  line-height: 50px;
  margin-left: 8px;

  .no-redirect {
    color: #97a8be;
    cursor: text;
  }
}
</style>
