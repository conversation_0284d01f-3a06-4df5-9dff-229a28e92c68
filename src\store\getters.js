const getters = {
  sidebar: (state) => state.app.sidebar,
  language: (state) => state.app.language,
  size: (state) => state.app.size,
  device: (state) => state.app.device,
  token: (state) => state.user.token,
  avatar: (state) => state.user.avatar,
  userId: (state) => state.user.userId,
  name: (state) => state.user.name,
  userName: (state) => state.user.userName,
  introduction: (state) => state.user.introduction,
  routesMap: (state) => state.user.routesMap,
  permission_routes: (state) => state.permission.routes,

  ruleConfig: (state) => state.conditions.ruleConfig,
  tableRuleConfig: (state) => state.tableCondition.ruleConfig, // 表格检测规则配置
  stypeIndex: (state) => state.conditions.stypeIndex,
  rulesForm: (state) => state.conditions.form,
  tableRulesForm: (state) => state.tableCondition.tableForm, //表格检测表单
  projectName: (state) => state.project.projectName,
  projectBranch: (state) => state.project.projectBranch,
  projectCardIndex: (state) => state.project.projectCardIndex,
  // 分支选择器相关getters
  branchOptions: (state) => state.project.branchOptions,
  branchData: (state) => state.project.branchData,
  selectedBranch: (state) => state.project.selectedBranch,
  selectedCheckType: (state) => state.project.selectedCheckType,
  checkTypeOptions: (state) => state.project.checkTypeOptions,
  branchLoading: (state) => state.project.loading,
  selectedBranchInfo: (state) => state.project.selectedBranchInfo,
};
export default getters;
