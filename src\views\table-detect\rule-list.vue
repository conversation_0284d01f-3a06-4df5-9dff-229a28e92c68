<template>
  <div class="app-container">
    <div class="list">
      <div class="el-page-header">
        <div class="el-page-header__content">
          {{ $t("rulesList.rulesList") }}
        </div>
      </div>
      <el-divider />
      <div style="margin-bottom: 20px">
        <el-input
          v-model="listQuery.keyword"
          style="width: 15%; margin-right: 5px"
          placeholder="请输入关键字模糊搜索"
          clearable
          @clear="searchRule()"
        />
        <el-button type="primary" icon="el-icon-search" @click="searchRule()"
          >搜索</el-button
        >
        <el-button type="success" @click="goRuleCreate">
          {{ $t("rulesList.createRule") }}</el-button
        >
        <el-button type="danger" @click="delMultipleRules">{{
          $t("rulesList.deleteRule")
        }}</el-button>
      </div>
      <el-table
        ref="ruleTableRef"
        :data="tableData"
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="ruleName" label="规则名" />
        <el-table-column prop="table_headers" label="表格信息" min-width="120">
          <template slot-scope="scope">
            <el-tag
              v-for="(item, index) in scope.row.table_headers"
              type="primary"
              :style="{ marginLeft: index > 0 ? '5px' : '' }"
              >{{ item.tableName + " - " + item.sheet }}</el-tag
            >
          </template>
        </el-table-column>
        <el-table-column prop="branch" label="检查分支">
          <template slot-scope="scope">
            <!-- <el-tag
              v-for="(item, index) in scope.row.checkBranch"
              type="primary"
              :style="{ marginLeft: index > 0 ? '5px' : '' }"
              >{{ item }}</el-tag
            > -->
            <el-tag>{{ scope.row.branch }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="rank" label="规则等级" width="80" />
        <el-table-column
          prop="description"
          label="错误提示"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column prop="username" label="最后修改人" width="100" />
        <el-table-column
          prop="effective_count"
          label="生效次数"
          align="center"
          width="80"
        >
          <template slot-scope="scope">
            <el-link type="primary" @click="goReportDetails(scope.row)">{{
              scope.row.effective_count
            }}</el-link>
          </template>
        </el-table-column>
        <el-table-column
          label="是否启用"
          fixed="right"
          width="80"
          align="center"
        >
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.isEnabled"
              @change="changeEnable(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="120" align="center">
          <template slot-scope="scope">
            <el-link
              style="margin-right: 20px"
              type="primary"
              :underline="false"
              @click="editRule(scope.row)"
              >编辑</el-link
            >
            <el-popconfirm
              title="确定删除此规则吗？"
              @confirm="delRule(scope.row)"
            >
              <el-link slot="reference" type="danger" :underline="false"
                >删除</el-link
              >
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <div style="display: flex; justify-content: end">
        <pagination
          style="margin-top: 10px; padding: 10px 20px"
          :total="total"
          :page.sync="listQuery.page"
          :limit.sync="listQuery.limit"
          @pagination="pageSwitch"
        />
      </div>
    </div>
  </div>
</template>

<script>
import Pagination from "@/components/Pagination/index.vue";
import { mapGetters } from "vuex";
import { getExcelRuleList, deleteRule, updateRuleStatus } from "@/api/rule";
import { getProjectBranchs, getUserProjectList } from "@/api/project";

export default {
  components: { Pagination },
  data() {
    return {
      tableData: [],
      total: 0,
      listQuery: {
        keyword: "",
        page: 1,
        limit: 20,
      },
      multipleSelection: [],
    };
  },
  computed: {
    ...mapGetters(["rulesForm"]),
  },
  mounted() {
    this.getRulesList();
  },

  methods: {
    // 前往创建规则页
    goRuleCreate() {
      this.$router.push("/tabledetect/rulecreate");
    },

    // 获取规则列表数据
    async getRulesList() {
      let params = {
        isExcel: "true",
        // projectId: this.projectId,
        // branch: this.projectBranch,
        search: this.listQuery.keyword,
        pageNum: this.listQuery.page,
        pageSize: this.listQuery.limit,
      };
      let { code, data, count } = await getExcelRuleList(params);
      if (code === 200) {
        this.tableData = data;
        this.total = count;
      }
    },
    // 搜索规则
    searchRule() {
      this.listQuery.page = 1;
      this.getRulesList();
    },
    // 点击查看/编辑规则详情 复用规则参数到新建规则页面
    async editRule(rule) {
      // 获取规则编辑详情的rule_config配置
      // const params = {
      //   projectId: rule.projectInfo_id,
      //   branch: rule.branch,
      // };
      // let { code, json } = await getRuleConfig(params);
      // if (code === 200) {
      //   if (json.length <= 0) {
      //     this.$message.error("Invalid rule!Unable to get rule_config!");
      //     return;
      //   }
      //   this.$store.commit("conditions/setRuleConfig", json);
      //   let stypeIndex = (json || []).findIndex(
      //     (item) => item.type === rule.stype
      //   );
      //   this.$store.commit("conditions/setStypeIndex", stypeIndex); //把获取的类型索引值保存
      // }
      this.$router.push({
        path: "/tableruleedit",
        query: { ruleId: rule.ruleId },
      });
    },
    // 是否启用规则
    async changeEnable(row) {
      const { code, msg } = await updateRuleStatus({
        rule_id: row.ruleId,
        is_enabled: row.isEnabled,
      });
      if (code === 200) {
        this.$message.success(msg);
      }
    },
    // 删除规则
    async delRule(row) {
      const { code, msg } = await deleteRule({ ruleId: row.ruleId });
      if (code === 200) {
        this.$message.success(msg);
        this.getRulesList();
      }
    },
    // 选中的规则id数据
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 删除多选选中的规则
    async delMultipleRules() {
      if (this.multipleSelection.length <= 0) {
        this.$message.warning("请选择要删除的规则！");
        return;
      }
      const ids = this.multipleSelection.map((item) => item.ruleId);
      // console.log(ids.toString());
      const { code, msg } = await deleteRule({ ruleId: ids.toString() });
      if (code === 200) {
        this.$message.success(msg);
        this.getRulesList();
      }
    },
    // 点击次数前往规则报告页
    goReportDetails(row) {
      this.$router.push({
        path: "/tabledetect/rulereport",
        query: {
          reportId: row.ruleId,
        },
      });
    },

    // 分页器页码改变获取数据
    pageSwitch(value) {
      this.getRulesList();
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep.fixed-header + .app-main {
  background-color: #f6f8f9 !important;
}

.app-container {
  .list {
    padding: 20px;
    background-color: #fff;
  }
}
// ::v-deep.el-tag {
//   max-width: 200px; /* 设置最大宽度 */
//   white-space: nowrap; /* 禁止换行 */
//   overflow: hidden; /* 超出部分隐藏 */
//   text-overflow: ellipsis; /* 使用省略号表示超出文本 */
// }
::v-deep .el-radio {
  margin-right: 5px;
}

::v-deep .el-dialog__body {
  padding: 10px 20px;
}

::v-deep .el-checkbox__label {
  font-size: 16px;
}

.dialog-form {
  margin-bottom: 10px;

  .form-title {
    font-size: 14px;
    font-weight: 700;
    color: #606266;
  }
}
</style>
