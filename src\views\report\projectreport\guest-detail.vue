<template>
  <div class="app-container">
    <div class="report-detail">
      <div>
        <el-descriptions
          :title="$t('reportDetail.reportDetail')"
          :column="2"
          style="font-size: 16px"
        >
          <el-descriptions-item :label="$t('reportDetail.testId')">{{
            $route.query.reportId
          }}</el-descriptions-item>
          <el-descriptions-item :label="$t('reportDetail.scanTime')">{{
            reportDetail.scan_time
          }}</el-descriptions-item>
          <el-descriptions-item :label="$t('reportDetail.testVersion')">{{
            reportDetail.branch
          }}</el-descriptions-item>
          <el-descriptions-item :label="$t('reportDetail.errorCount')">{{
            reportDataLen
          }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <div style="margin-top: 20px">
        <report-detail
          :tableTitle="$t('reportDetail.reportList')"
          :projectId="$route.query.projectId"
          :branch="reportDetail.branch"
        />
      </div>
    </div>
    <!-- 返回顶部组件 -->
    <el-tooltip placement="top" :content="$t('backToTop')">
      <back-to-top
        :visibility-height="300"
        :back-position="50"
        transition-name="fade"
      ></back-to-top>
    </el-tooltip>
  </div>
</template>

<script>
import ReportDetail from "./components/ReportDetail.vue";
import { getReportDetail } from "@/api/project";
import BackToTop from "@/components/BackToTop";
export default {
  name: "GuestReportDetail",
  components: { ReportDetail, BackToTop },
  data() {
    return {
      reportDetail: {},
      reportData: [],
      reportDataLen: 0,
    };
  },
  mounted() {
    this.getReportDetail();
  },

  methods: {
    // 获取报告详情
    async getReportDetail() {
      let id = this.$route.query.reportId;
      const params = {
        projectId: this.$route.query.projectId,
        id,
        pageNum: 1,
        pageSize: 30,
      };
      const { code, branch, data, dataLen } = await getReportDetail(params);
      if (code === 200) {
        this.reportDetail = branch;
        this.reportData = data;
        this.reportDataLen = dataLen;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  .report-detail {
    background-color: #fff;
    padding: 15px;
    padding-top: 25px;
    position: relative;
  }
}
</style>
