<template>
  <div class="app-container">
    <div class="md-body">
      <markdown01 />
      <markdown01 />
      <markdown01 />
      <el-divider></el-divider>
      <div class="page-nav">
        <p class="inner">
          <span class="prev">
            <i class="el-icon-back"></i>
            <router-link to="/"> 首页 </router-link></span
          >
          <span class="next"
            ><router-link to="/helpguide/guide2"> 帮助文档02 </router-link>
            <i class="el-icon-right"></i>
          </span>
        </p>
      </div>
      <el-tooltip placement="top" :content="$t('backToTop')">
        <back-to-top
          :visibility-height="300"
          :back-position="50"
          transition-name="fade"
        />
      </el-tooltip>
    </div>
  </div>
</template>

<script>
// 引入 markdown 文件，引入后是一个组件，需要在 components 中注册
import markdown01 from "./components/markdown01.md";
import BackToTop from "@/components/BackToTop";
export default {
  components: { markdown01, BackToTop },
};
</script>

<style lang="scss" scoped>
.app-container {
  width: 100%;
  display: flex;
  justify-content: center;
}
.md-body {
  width: 820px;
  padding: 1.2rem;
  background-color: #fff;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Arial, sans-serif;
}

@media (max-width: 820px) {
  .md-body {
    width: 100%;
  }
}

.page-nav .inner {
  display: flex;
  justify-content: space-between;
  a {
    font-size: 18px;
    color: #3eaf7c;
    font-weight: 500;
  }
}
</style>