<template>
  <div class="app-container">
    <div class="list">
      <div class="el-page-header">
        <div class="el-page-header__content">
          {{ $t("rulesList.rulesList") }}
        </div>
      </div>
      <el-divider />
      <el-button type="primary" size="large" @click="goRuleCreate">
        {{ $t("rulesList.createRule") }}</el-button
      >
      <el-button type="success" size="large" @click="importRules">
        {{ "批量导入规则" }}</el-button
      >
      <el-button type="danger" size="large" @click="delRules">{{
        $t("rulesList.deleteRule")
      }}</el-button>
      <div style="margin: 20px 0">
        <span>{{ $t("rulesList.filterProject") }}：</span>
        <el-select
          v-model="projectId"
          :placeholder="$t('rulesList.selectProject')"
          clearable
          style="width: 200px; margin-right: 10px"
          @change="changeProject"
          @clear="clearSelectProject"
        >
          <el-option
            v-for="(item, index) in userProjectList"
            :key="index"
            :label="item.projectName"
            :value="item.projectId"
          />
        </el-select>
        <el-select
          v-model="projectBranch"
          :placeholder="$t('rulesList.selectBranch')"
          style="width: 150px"
          @change="changeBranch"
        >
          <el-option
            v-for="(item, index) in projectBranchData"
            :key="index"
            :label="item.branch"
            :value="item.branch"
          />
        </el-select>
      </div>
      <div style="margin: 20px 0">
        <span>{{ $t("rulesList.ruleCategories") }}：</span>
        <el-select
          v-model="ruleStype"
          name="stype"
          :placeholder="$t('rulesList.selectRuleCategory')"
          style="width: 300px"
        >
          <el-option :label="$t('rulesList.allType')" value="all"></el-option>
          <el-option
            v-for="(item, index) in ruleConfig"
            :key="index"
            :label="item.type"
            :value="item.type"
          />
        </el-select>
      </div>
      <div style="margin: 20px 0">
        <span>规则形式：</span>
        <el-radio-group v-model="is_general" @input="changeRuleForm">
          <el-radio :label="false">自定义规则</el-radio>
          <el-radio :label="true">通用规则</el-radio>
        </el-radio-group>
      </div>
      <el-checkbox
        v-model="checkAll"
        :indeterminate="isIndeterminate"
        @change="handleCheckAllChange"
        >{{ $t("rulesList.selectAll") }}</el-checkbox
      >
      <el-checkbox-group
        v-model="checkedRules"
        @change="handleCheckedRulesChange"
      >
        <div
          v-for="rule in rulesListData"
          :key="rule.ruleId"
          style="margin: 15px 0"
          class="rule_list_item_warp"
        >
          <el-checkbox :label="rule">
            <div
              :style="{
                color: rule.isEnabled ? '' : '#999',
                textDecoration: rule.isEnabled ? '' : 'line-through',
              }"
              @click.stop.prevent="checkRule(rule)"
            >
              <div style="display: flex; align-items: center">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="rule.ruleName"
                  placement="top-start"
                >
                  <span class="text-ellipsis">{{ rule.ruleName }}</span>
                </el-tooltip>
                <el-tag size="mini" type="success" style="margin-left: 15px">{{
                  rule.stype
                }}</el-tag>
              </div>
            </div>
          </el-checkbox>
          <el-switch
            v-model="rule.isEnabled"
            active-color="#13ce66"
            inactive-color="#ff4949"
            active-text="启用规则"
            inactive-text="忽略规则"
            class="my_switch"
            @change="changeEbanledStatus(rule)"
          >
            >
          </el-switch>
        </div>
      </el-checkbox-group>
      <pagination
        style="margin-top: 0; padding: 10px 20px"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="pageSwitch"
      />
    </div>
    <!-- 批量导入规则弹窗 -->
    <el-dialog
      :title="'批量导入规则'"
      :visible.sync="dialogImportVisible"
      :close-on-click-modal="false"
      width="40%"
      @close="closeUpload"
    >
      <el-dialog
        :title="'导入文件错误信息'"
        :visible.sync="dialogErrorVisible"
        :close-on-click-modal="false"
        append-to-body
        width="40%"
        @close="dialogErrorVisible = false"
      >
        <el-alert
          title="导入失败，请将以下文件错误修改后重新上传！"
          type="error"
          show-icon
          :closable="false"
          style="margin-bottom: 10px"
        >
        </el-alert>
        <el-descriptions
          :column="1"
          border
          style="height: 400px; overflow: auto"
        >
          <el-descriptions-item
            v-for="(value, key, index) in errorUploadData"
            :key="index"
            :label="key"
          >
            {{ value.join("\n") }}
          </el-descriptions-item>
        </el-descriptions>
        <span slot="footer">
          <el-button size="mini" @click="dialogErrorVisible = false">{{
            $t("project.close")
          }}</el-button>
        </span>
      </el-dialog>
      <el-form ref="importValidateForm" :inline="true" :model="projectForm">
        <el-form-item
          :label="$t('ruleCreate.projectBranch')"
          prop="projectId"
          :rules="[
            { required: true, message: '请选择项目', trigger: 'change' },
          ]"
        >
          <el-select
            v-model="projectForm.projectId"
            :placeholder="$t('ruleCreate.selectProject')"
            style="width: 200px; margin-right: 10px"
            @change="changeImportProject"
          >
            <el-option
              v-for="(item, index) in userProjectList"
              :key="index"
              :label="item.projectName"
              :value="item.projectId"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          prop="projectBranch"
          :rules="[
            { required: true, message: '请选择分支', trigger: 'change' },
          ]"
        >
          <el-select
            v-model="projectForm.projectBranch"
            :placeholder="$t('ruleCreate.selectBranch')"
          >
            <el-option
              v-for="(item, index) in importBranchs"
              :key="index"
              :label="item.branch"
              :value="item.branch"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div style="text-align: center">
        <el-upload
          style="margin-bottom: 20px"
          ref="uploadFile"
          drag
          action=""
          accept=""
          :on-change="fileChange"
          :on-remove="fileRemove"
          :auto-upload="false"
          :multiple="true"
          :file-list="uploadFilesList"
          :show-file-list="false"
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">
            {{ $t("project.uploadTips1")
            }}<em>{{ $t("project.uploadTips2") }}</em>
          </div>
        </el-upload>
        <el-table
          v-loading="loading"
          :element-loading-text="$t('project.uploading')"
          :data="uploadFilesList"
          max-height="300"
          :show-header="false"
        >
          <el-table-column
            prop="name"
            :show-overflow-tooltip="true"
            label="File Name"
          >
            <template slot-scope="scope">
              <i style="color: #409eff" class="el-icon-s-order" />
              {{ scope.row.name }}
            </template>
          </el-table-column>
          <el-table-column
            prop="name"
            width="100"
            fixed="right"
            align="center"
            :show-overflow-tooltip="true"
            label="Operate"
          >
            <template slot-scope="scope">
              <el-button
                type="danger"
                icon="el-icon-delete"
                circle
                size="mini"
                @click.native.prevent="delFile(scope.$index)"
              />
            </template>
          </el-table-column>
        </el-table>
      </div>
      <span slot="footer">
        <el-button size="mini" type="primary" @click="uploadConfirm">{{
          $t("project.upload")
        }}</el-button>
        <el-button size="mini" @click="closeUpload">{{
          $t("project.close")
        }}</el-button>
      </span>
    </el-dialog>
    <!-- 上传报错提示弹窗 -->
    <el-dialog
      :title="'导入文件错误信息'"
      :visible.sync="dialogErrorVisible"
      :close-on-click-modal="false"
      append-to-body
      width="40%"
      @close="dialogErrorVisible = false"
    >
      <el-descriptions :column="1" border style="height: 400px; overflow: auto">
        <el-descriptions-item
          v-for="(value, key, index) in errorUploadData"
          :key="index"
          :label="key"
        >
          {{ value.toString() }}
        </el-descriptions-item>
      </el-descriptions>
      <span slot="footer">
        <el-button size="mini" @click="dialogErrorVisible = false">{{
          $t("project.close")
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from "@/components/Pagination/index.vue";
import { mapGetters } from "vuex";
import {
  getRuleList,
  deleteRule,
  getRuleConfig,
  updateRuleEnabled,
} from "@/api/rule";
import { getProjectBranchs, getUserProjectList } from "@/api/project";
import { uploadRules } from "@/api/upload";

export default {
  components: { Pagination },
  data() {
    return {
      userProjectList: [],
      projectId: "",
      projectBranchData: [],
      projectBranch: "",

      ruleConfig: [], //保存规则分类
      ruleStype: "all", // 单选选中框 默认all
      checkAll: false, // 全选框是否勾选
      checkedRules: [], // 选中的规则数组
      rulesListData: [],
      isIndeterminate: false,
      total: 0,
      listQuery: {
        page: 1,
        limit: 10,
      },
      isEnableRule: true, // 是否开启了规则
      ruleId: 0,
      ruleData: {}, // 后台请求的某条数据

      dialogImportVisible: false, // 控制开启批量导入弹窗
      uploadFilesList: [], // 上传的文件显示在下方列表
      loading: false, //上传加载动画
      importBranchs: [],
      projectForm: {
        projectId: "",
        projectBranch: "",
      },
      errorUploadData: {}, //上传保存数据
      dialogErrorVisible: false,
      is_general: false, // 是否是通用规则
    };
  },
  computed: {
    ...mapGetters(["rulesForm"]),
  },
  watch: {
    ruleStype(newval, odlval) {
      this.getRulesList();
    },
  },
  mounted() {
    this.getUserProjectList();
    // this.getRulesList();
  },
  created() {
    //在页面刷新时将form数据保存到localStorage里
    window.addEventListener("beforeunload", () => {
      localStorage.setItem("rule_projectId", JSON.stringify(this.projectId));
      localStorage.setItem(
        "rule_projectBranch",
        JSON.stringify(this.projectBranch)
      );
      localStorage.setItem("rule_ruleStype", JSON.stringify(this.ruleStype));
    });
  },
  //在页面离开时记录form数据
  beforeRouteLeave(to, from, next) {
    localStorage.setItem("rule_projectId", JSON.stringify(this.projectId));
    localStorage.setItem(
      "rule_projectBranch",
      JSON.stringify(this.projectBranch)
    );
    localStorage.setItem("rule_ruleStype", JSON.stringify(this.ruleStype));
    next();
  },

  methods: {
    // 批量导入规则
    importRules() {
      this.dialogImportVisible = true;
    },
    changeImportProject(val) {
      this.getImportBranchs(val);
      this.projectForm.projectBranch = "";
    },
    async getImportBranchs(projectId) {
      let { code, data } = await getProjectBranchs({ projectId });
      if (code == 200) {
        this.importBranchs = data;
      }
    },
    // 上传文件之前
    beforeUpload(file) {
      this.uploadFilesList.forEach((item) => {
        if (isEquael(item.fileName, file.name)) {
          return this.$message.warning(this.$t("project.fileExists"));
        }
      });
    },
    // 文件上传时的钩子
    fileChange(file, fileList) {
      // 判断文件名是否重复
      let count = 0;
      fileList.forEach((item, idx) => {
        if (file.name == item.name) {
          count++;
          if (count === 2) {
            setTimeout(() => {
              this.$message({
                message: file.name + this.$t("project.existed"),
                type: "info",
              });
            }, 10);
            fileList.pop(); // 相同则删除新增进来的文件
          }
        }
      });

      this.uploadFilesList = fileList;
    },
    fileRemove(file, fileList, name) {
      this.uploadFilesList = fileList;
    },
    delFile(index) {
      this.uploadFilesList.splice(index, 1);
    },

    closeUpload() {
      this.uploadFilesList = [];
      this.loading = false;
      this.dialogImportVisible = false;
    },
    // 确认上传文件到服务器
    uploadConfirm() {
      this.$refs.importValidateForm.validate(async (valid) => {
        if (valid) {
          // 判断是否有文件再上传
          if (this.uploadFilesList.length === 0) {
            return this.$message.warning(this.$t("project.selectFiles"));
          }
          // 下面的代码将创建一个空的FormData对象:
          const formData = new FormData();
          // 使用FormData.append来添加键/值对到表单里面；
          this.uploadFilesList.forEach((file) => {
            formData.append("files", file.raw);
          });
          // 添加自定义参数
          formData.append("projectId", this.projectForm.projectId);
          formData.append("branch", this.projectForm.projectBranch);
          // formData.append("is_res", true); //仅测试传值,正式需后端上传原始资源
          if (!this.loading) {
            this.loading = true;
            let { code, msg, data } = await uploadRules(formData);
            if (code !== 200) {
              this.errorUploadData = data;
              this.dialogErrorVisible = true;
              // this.$message.warning("导入失败!");
              this.loading = false;
              return;
            }
            this.$message.success(this.$t("project.uploadSuccess"));
            this.loading = false;
            this.closeUpload();
            this.getRulesList();
          }
        }
      });

      //   if (code === 306) {
      //     this.$message.error(this.$t("project.wrongFormat"));
      //     this.uploadFilesList = [];
      //     this.loading = false;
      //     return;
      //   } else {
      //     this.$message.error(msg);
      //     this.loading = false;
      //   }
      // } else {
      //   this.$message.warning(this.$t("project.uploading"));
      // }
    },

    // 获取所有项目
    async getUserProjectList() {
      let { code, projectList, dataLen } = await getUserProjectList();
      if (code === 200) {
        this.userProjectList = projectList;
        if (localStorage.getItem("rule_ruleStype")) {
          this.ruleStype = JSON.parse(localStorage.getItem("rule_ruleStype"));
          localStorage.removeItem("rule_ruleStype");
        }
        if (
          localStorage.getItem("rule_projectId") &&
          localStorage.getItem("rule_projectBranch")
        ) {
          let id = JSON.parse(localStorage.getItem("rule_projectId"));
          // 判断如果项目已被删除,则清除数据
          let findId = projectList.some((item) => {
            return item.projectId === id;
          });
          if (findId) {
            let branch = JSON.parse(localStorage.getItem("rule_projectBranch"));
            this.projectId = id;
            this.getProjectBranchs(id);
            this.projectBranch = branch;
          } else {
            this.getRulesList(); //获取规则列表
          }

          localStorage.removeItem("rule_projectId");
          localStorage.removeItem("rule_projectBranch");
        } else {
          this.getRulesList(); //获取规则列表
        }
      }
    },
    // 获取项目所有报告分支
    async getProjectBranchs(projectId) {
      let { code, data } = await getProjectBranchs({ projectId });
      if (code === 200) {
        this.projectBranchData = data;
        if (!this.projectBranch && data.length > 0) {
          this.projectBranch = this.projectBranchData[0].branch;
          this.getRuleConfig(); //获取规则分类
          this.getRulesList(); //重新加载规则列表
        } else {
          this.getRuleConfig(); //获取规则分类
          this.getRulesList(); //重新加载规则列表
        }
      }
    },
    // 切换项目
    changeProject(val) {
      this.projectBranch = "";
      this.getProjectBranchs(this.projectId);
    },
    // 切换分支
    changeBranch(val) {
      this.getRuleConfig(); //获取规则分类
      this.getRulesList(); //重新加载规则列表
    },
    //清空选择的项目
    clearSelectProject() {
      this.ruleStype = "all";
      this.getRuleConfig(); //获取规则分类
    },
    // 获取规则分类
    async getRuleConfig() {
      let params = {
        projectId: this.projectId,
        branch: this.projectBranch,
      };
      let { code, json } = await getRuleConfig(params);
      if (code === 200) {
        this.ruleConfig = json;
      }
    },

    // 前往创建规则页
    goRuleCreate() {
      this.$router.push("/resourcedetect/rulecreate");
    },
    // 全选中的数据
    handleCheckAllChange(val) {
      this.checkedRules = val ? this.rulesListData : [];
      this.isIndeterminate = false;
      // console.log(this.checkedRules);
    },
    // 选中的变化
    handleCheckedRulesChange(value) {
      const checkedCount = value.length;
      this.checkAll = checkedCount === this.rulesListData.length;
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.rulesListData.length;
      // console.log(value);
    },

    // 删除选中的规则
    delRules() {
      if (this.checkedRules.length === 0) {
        this.$message("未选中任何规则");
        return;
      }
      this.$confirm("此操作将删除选中的规则, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          let ruleId = [];
          this.checkedRules.forEach((item) => {
            ruleId.push(item.ruleId);
          });
          let { code, msg } = await deleteRule({ ruleId: ruleId.toString() });
          if (code === 200) {
            this.getRulesList();
            this.$message({
              type: "success",
              message: msg,
            });
          }
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 获取规则分类列表数据
    async getRulesList() {
      // console.log(stype);
      let params = {
        projectId: this.projectId,
        branch: this.projectBranch,
        stype: this.ruleStype,
        is_general: this.is_general,
        pageNum: this.listQuery.page,
        pageSize: this.listQuery.limit,
      };
      let { code, data, dataLen } = await getRuleList(params);
      if (code === 200) {
        this.rulesListData = data;
        this.total = dataLen;
      }
    },
    // 点击查看/编辑规则详情 复用规则参数到新建规则页面
    async checkRule(rule) {
      // 获取规则编辑详情的rule_config配置
      // const params = {
      //   projectId: rule.projectInfo_id,
      //   branch: rule.branch,
      // };
      // let { code, json } = await getRuleConfig(params);
      // if (code === 200) {
      //   if (json.length <= 0) {
      //     this.$message.error("Invalid rule!Unable to get rule_config!");
      //     return;
      //   }
      //   this.$store.commit("conditions/setRuleConfig", json);
      //   let stypeIndex = (json || []).findIndex(
      //     (item) => item.type === rule.stype
      //   );
      //   this.$store.commit("conditions/setStypeIndex", stypeIndex); //把获取的类型索引值保存
      // }
      this.$router.push({
        path: "/ruleedit",
        query: { ruleId: rule.ruleId },
      });
    },

    // 分页器页码改变获取数据
    pageSwitch(value) {
      this.getRulesList();
    },

    // 切换展示的规则内容
    changeRuleForm() {
      this.checkAll = false;
      this.getRulesList();
    },

    // 禁用和启用规则
    async changeEbanledStatus(val) {
      const { ruleId, isEnabled } = val;
      await updateRuleEnabled({ ruleId, isEnabled });
      this.$message({
        type: "success",
        message: this.$t("rulesList.operateSuccess"),
      });
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep.fixed-header + .app-main {
  background-color: #f6f8f9 !important;
}

.app-container {
  .list {
    padding: 20px;
    background-color: #fff;
  }
}

::v-deep .el-radio {
  margin-right: 5px;
}

::v-deep .el-dialog__body {
  padding: 10px 20px;
}
::v-deep .el-checkbox__label {
  width: 100%;
  font-size: 16px;
}
.text-ellipsis {
  white-space: nowrap; /* 文本不换行 */
  overflow: hidden; /* 隐藏超出的部分 */
  text-overflow: ellipsis; /* 超出部分显示为省略号 */
  // width: 60%; /* 最大宽度，可以根据需要调整 */
  // width: 90%; /* 最大宽度，可以根据需要调整 */
}

.dialog-form {
  margin-bottom: 10px;

  .form-title {
    font-size: 14px;
    font-weight: 700;
    color: #606266;
  }
}

::v-deep .el-form--inline .el-form-item {
  margin-right: 0;
}
.rule_list_item_warp {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.my_switch {
  margin-right: 100px;
}
::v-deep .el-checkbox {
  width: 80%;
}
</style>
