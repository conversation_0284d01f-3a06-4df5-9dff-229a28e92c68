
import { getProjectBranchs } from '@/api/project'

const state = {
  projectId: sessionStorage.getItem('currentProjectId') || null,
  projectName: sessionStorage.getItem('currentProjectName') || '',
  projectBranch: sessionStorage.getItem('currentProjectBranch') || '',
  projectCardIndex: 0,
  // 分支选择器相关数据
  branchOptions: [], // 分支列表
  branchData: [], // 完整的分支数据（包含art和table字段）
  selectedBranch: '', // 选中的分支
  selectedCheckType: null, // 选中的检查类型
  checkTypeOptions: [], // 检查类型选项（动态数据，默认为空数组）
  loading: false, // 加载状态
}

const mutations = {
  setProjectId(state, id) {
    state.projectId = id
    // 同步到sessionStorage
    if (id) {
      sessionStorage.setItem('currentProjectId', id)
    } else {
      sessionStorage.removeItem('currentProjectId')
    }
  },
  setProjectName(state, name) {
    state.projectName = name
    // 同步到sessionStorage
    if (name) {
      sessionStorage.setItem('currentProjectName', name)
    } else {
      sessionStorage.removeItem('currentProjectName')
    }
  },
  setProjectBranch(state, branch) {
    state.projectBranch = branch
    // 同步到sessionStorage
    if (branch) {
      sessionStorage.setItem('currentProjectBranch', branch)
    } else {
      sessionStorage.removeItem('currentProjectBranch')
    }
  },
  setProjectCardIndex(state, index) {
    state.projectCardIndex = index
  },
  // 分支选择器相关mutations
  setBranchOptions(state, options) {
    state.branchOptions = options
  },
  setBranchData(state, data) {
    state.branchData = data
  },
  setSelectedBranch(state, branch) {
    state.selectedBranch = branch
  },
  setSelectedCheckType(state, checkType) {
    state.selectedCheckType = checkType
  },
  setCheckTypeOptions(state, options) {
    state.checkTypeOptions = options
  },
  setLoading(state, loading) {
    state.loading = loading
  },
  // 清空项目相关数据
  clearProjectData(state) {
    state.branchOptions = []
    state.branchData = []
    state.selectedBranch = ''
    state.selectedCheckType = null
    state.checkTypeOptions = []
    state.loading = false
  },
}

const actions = {
  // 获取分支列表
  async fetchBranchOptions({ commit, dispatch, state }, projectId) {
    if (!projectId) {
      console.log('未找到projectId，跳过分支数据获取')
      return
    }

    try {
      commit('setLoading', true)
      const response = await getProjectBranchs({ projectId })

      if (response.code === 200 && response.data && response.data.length > 0) {
        // 保存完整的分支数据
        commit('setBranchData', response.data)

        // 转换数据格式
        const branchOptions = response.data.map(item => ({
          value: item.branch,
          label: item.branch
        }))

        commit('setBranchOptions', branchOptions)

        // 从session中恢复之前选择的分支
        const savedBranch = sessionStorage.getItem(`selectedBranch_${projectId}`)
        console.log('savedBranch:', savedBranch)
        if (savedBranch && branchOptions.some(option => option.value === savedBranch)) {
          // 如果session中有保存的分支且在当前分支列表中，则选中它
          commit('setSelectedBranch', savedBranch)
        } else {
          // 否则默认选中第一个分支
          const defaultBranch = branchOptions[0].value
          console.log('默认选中第一个分支:', defaultBranch)
          commit('setSelectedBranch', defaultBranch)
          // 保存到session
          sessionStorage.setItem(`selectedBranch_${projectId}`, defaultBranch)
        }

        // 根据选中的分支生成检查类型选项
        dispatch('updateCheckTypeOptions', savedBranch || branchOptions[0].value)
      } else {
        console.warn('分支数据为空或格式错误')
        // 清空数据
        commit('setBranchData', [])
        commit('setBranchOptions', [])
        commit('setCheckTypeOptions', [])
        commit('setSelectedBranch', '')
        commit('setSelectedCheckType', null)
      }
    } catch (error) {
      console.error('获取分支数据失败:', error)
      throw error
    } finally {
      commit('setLoading', false)
    }
  },

  // 更新选中的分支
  updateSelectedBranch({ commit, dispatch }, { branch, projectId }) {
    commit('setSelectedBranch', branch)

    // 保存到session
    if (projectId) {
      sessionStorage.setItem(`selectedBranch_${projectId}`, branch)
    }

    // 更新检查类型选项
    dispatch('updateCheckTypeOptions', branch)
  },

  // 更新选中的检查类型
  updateSelectedCheckType({ commit }, checkType) {
    commit('setSelectedCheckType', checkType)
  },

  // 根据选中的分支更新检查类型选项
  updateCheckTypeOptions({ commit, state }, selectedBranch) {
    if (!selectedBranch || !state.branchData || state.branchData.length === 0) {
      commit('setCheckTypeOptions', [])
      commit('setSelectedCheckType', null)
      return
    }

    // 找到选中的分支数据
    const branchInfo = state.branchData.find(item => item.branch === selectedBranch)

    if (!branchInfo) {
      commit('setCheckTypeOptions', [])
      commit('setSelectedCheckType', null)
      return
    }

    // 根据art和table字段生成检查类型选项
    const checkTypeOptions = []

    if (branchInfo.art) {
      checkTypeOptions.push({ value: 1, label: '美术资源' })
    }

    if (branchInfo.table) {
      checkTypeOptions.push({ value: 2, label: '表格资源' })
    }

    commit('setCheckTypeOptions', checkTypeOptions)

    // 设置默认选中的检查类型
    if (checkTypeOptions.length > 0) {
      commit('setSelectedCheckType', checkTypeOptions[0].value)
    } else {
      commit('setSelectedCheckType', null)
    }
  },

  // 设置项目ID
  setProjectId({ commit }, projectId) {
    commit('setProjectId', projectId)
  },

  // 设置项目名称
  setProjectName({ commit }, projectName) {
    commit('setProjectName', projectName)
  },

  // 同时设置项目ID和项目名称
  setProjectInfo({ commit }, { projectId, projectName }) {
    commit('setProjectId', projectId)
    commit('setProjectName', projectName)
  },

  // 清除项目信息
  clearProjectInfo({ commit }) {
    commit('setProjectId', null)
    commit('setProjectName', '')
    commit('setProjectBranch', '')
  },

  // 清空项目数据（分支选择器相关）
  clearProjectData({ commit }) {
    commit('clearProjectData')
  },
}

const getters = {
  // 分支选择器相关getters
  branchOptions: state => state.branchOptions,
  branchData: state => state.branchData,
  selectedBranch: state => state.selectedBranch,
  selectedCheckType: state => state.selectedCheckType,
  checkTypeOptions: state => state.checkTypeOptions,
  loading: state => state.loading,
  // 获取当前选中分支的详细信息
  selectedBranchInfo: state => {
    if (!state.selectedBranch || !state.branchData || state.branchData.length === 0) {
      return null
    }
    return state.branchData.find(item => item.branch === state.selectedBranch)
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
}

