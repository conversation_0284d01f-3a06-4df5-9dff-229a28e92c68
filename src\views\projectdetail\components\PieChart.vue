<template>
  <div :class="className" :style="{ height: height, width: width }"></div>
</template>

<script>
import * as echarts from "echarts";
require("echarts/theme/macarons"); // echarts theme
import resize from "./mixins/resize";

export default {
  mixins: [resize],
  props: {
    pieChartData: {
      type: Array,
      required: true,
    },
    title: {
      type: String,
      default: "",
    },
    className: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "100%",
    },
  },
  data() {
    return {};
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  watch: {
    pieChartData: {
      deep: true,
      handler(val) {
        this.$nextTick(() => {
          this.initChart();
        });
      },
    },
  },
  methods: {
    // 初始化图表
    initChart() {
      this.$el.removeAttribute("_echarts_instance_"); // 移除容器上的 _echarts_instance_
      this.chart = echarts.init(this.$el, "macarons");

      this.chart.setOption({
        title: {
          text: this.title,
          // subtext: "",
          left: "left",
        },
        tooltip: {
          trigger: "item",
          // formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        legend: {
          // type: "scroll",
          // left: "center",
          // bottom: "50",
          type: "scroll",
          orient: "vertical",
          left: 10,
          top: 30,
        },
        series: [
          {
            name: this.$t("projectDetail.resourceData"),
            type: "pie",
            // roseType: "radius",
            radius: "60%",
            center: ["55%", "50%"],
            data: this.pieChartData,
            animationEasing: "cubicInOut",
            animationDuration: 2600,
          },
        ],
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>