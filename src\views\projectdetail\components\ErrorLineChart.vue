<template>
  <div
    v-if="errorChartData.length > 0"
    :class="className"
    :style="{ height: height, width: width }"
  ></div>
  <el-empty v-else :image-size="200"></el-empty>
</template>

<script>
import echarts from "echarts";
require("echarts/theme/macarons"); // echarts theme
import resize from "./mixins/resize";

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "650px",
    },
    autoResize: {
      type: Boolean,
      default: true,
    },
    // chartData: {
    //   type: Object,
    //   required: true,
    // },
    xAxisData: {
      type: Array,
      required: true,
    },
    errorChartData: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      chart: null,
      legendData: [],
      seriesData: [],
      selected: {},
      gridTop: "",
    };
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val);
      },
    },
    errorChartData: {
      deep: true,
      handler(val) {
        this.handleErrorChartData();
        this.$nextTick(() => {
          this.initChart();
        });
      },
    },
  },
  mounted() {
    // this.$nextTick(() => {
    //   this.initChart();
    // });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    // 数组转对象方法
    arrTransferObj(e, key, val) {
      // 数组的reduce方法，使数组的obj初始值为{}，将数组中每一个对象所需的值，分别作为对象中的键与值
      return e.reduce((obj, item) => ((obj[item[key]] = item[val]), obj), {});
    },
    // echart 修改grid.top值
    getGridTop(legendData) {
      var DEFAULT_LINE_NUM = 6; // 采用默认grid.top值的默认线条数目；
      var DEFAULT_GRID_TOP_PECENTAGE = 18; // 默认的grid.top百分比（整数部分）；
      var DELTA_GRID_TOP_PECENTAGE = 9; // 超出默认线条数时的grid.top百分比增量（整数部分）；
      var MAX_GRID_TOP_PECENTAGE = 20; // 最大的grid.top百分比（整数部分）；

      var topInt;
      var gridTop;
      var len = legendData.length;

      // 如果图例太多，需要调整option中的grid.top值才能避免重叠
      topInt =
        len > DEFAULT_LINE_NUM
          ? DEFAULT_GRID_TOP_PECENTAGE +
            DELTA_GRID_TOP_PECENTAGE * Math.ceil((len - DEFAULT_LINE_NUM) / 6)
          : DEFAULT_GRID_TOP_PECENTAGE;

      if (topInt >= MAX_GRID_TOP_PECENTAGE) {
        topInt = MAX_GRID_TOP_PECENTAGE;
      }

      gridTop = topInt + len / 4 + "%";

      return gridTop;
    },
    // 处理传来的数据
    handleErrorChartData() {
      // 保存legend
      let selectGroup = [];
      this.errorChartData.forEach((item, index) => {
        if (index < 1) {
          selectGroup.push({ type: item.type, isSelecte: true });
        } else {
          selectGroup.push({ type: item.type, isSelecte: false });
        }
        // 每个type的曲线
        this.seriesData.push({
          name: item.type,
          smooth: true,
          type: "line",
          data: item.count,
          animationDuration: 2800,
          animationEasing: "cubicInOut",
        });
      });
      // legend过多换行
      selectGroup.forEach((item, index) => {
        if (index % 6 == 0) {
          this.legendData.push("");
        }
        this.legendData.push(item.type);
      });
      this.gridTop = this.getGridTop(this.legendData);
      // 保存默认选中的数据
      this.selected = this.arrTransferObj(selectGroup, "type", "isSelecte");
      this.$nextTick(() => {
        this.initChart();
      });
    },
    initChart() {
      this.chart = echarts.init(this.$el, "macarons");
      // this.setOptions(this.chartData);
      this.setOptions();
    },
    // setOptions({ sameresource, mesh, texture, other } = {}) {
    setOptions() {
      this.chart.setOption({
        xAxis: {
          data: this.xAxisData,
          boundaryGap: false,
          axisTick: {
            show: false,
          },
          axisLabel: {
            interval: 0,
            rotate: 75,
            // textStyle: {
            //   fontSize: "12",
            // },
          },
        },
        grid: {
          left: 15,
          right: 10,
          bottom: 0,
          top: this.gridTop,
          containLabel: true,
        },
        tooltip: {
          trigger: "axis",
          formatter: function (params) {
            // 提示内容太多隔行显示内容
            let astr = "";
            params.forEach((ele, index) => {
              astr += `
                       <div style="display: block;height:20px;${
                         index % 2 === 0 ? "width: 50%;" : "width: 50%;"
                       }float:left;">
                        <i style="width: 10px;height: 10px;display: inline-block;background: ${
                          ele.color
                        };border-radius: 10px;"></i>
                            <span>${ele.seriesName}: ${ele.data}</span>
                        </div>
                        `;
            });
            const b = '<div style="width: 600px;">' + astr + "<div>";
            return b;
          },
          position: function (point, params, dom, rect, size) {
            return [point[1], 0];
          },
        },
        yAxis: {
          axisTick: {
            show: false,
          },
        },
        legend: {
          data: this.legendData,
          selected: this.selected,
          top: 0,
          // bottom: 0,
          // {
          //   Sameresource: true,
          //   Mesh: false,
          //   Texture2D: false,
          // },
        },
        series: this.seriesData,
      });
    },
  },
};
</script>
