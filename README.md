## 开发

```bash
# 克隆下载项目
git clone https://epartner-git.code.tencent.com/qaweb/qaweb.git

# 进入项目文件夹
cd qaweb

# 安装依赖
npm install --registry=https://registry.npmmirror.com

# 运行调试
npm run dev
```

浏览器访问 http://localhost:9527

## 发布

```bash
# 构建测试环境
npm run build:stage

# 构建生产环境
npm run build:prod
```

## 其他

```bash
# 预览发布环境效果
npm run preview

# 预览发布环境效果 + 静态资源分析
npm run preview -- --report

# 代码格式检查
npm run lint

# 代码格式检查并自动修复
npm run lint -- --fix
```
