export default {
  route: {
    dashboard: "Home",
    home: "Home",
    "报告管理": "Report Management",
    resourceDetect: "Resource Detect",
    tableDetect: "Table Detect",
    myProject: "My Projects",
    projectDetail: "Resource Project Detail",
    tableDetectDetail: "Table Project Detail",
    projectReport: "Project Report",
    tableProjectReport: "Table Project Report",
    reportDetail: "Report Detail",
    reportComparison: "Report Comparison",
    rulesList: "Rules List",
    ruleReport: "Rule Report",
    ruleEdit: "Edit Resource Rule",
    tableRuleEdit: "Edit Table Rule",
    ruleCreate: "Create Rule",
    permission: "Permissions",
    roleManagement: "Role Management",
    projectManagement: "Resource Project Management",
    // tableManagement: "Table Project Management",
    tableManagement: "Project Management",

    userManagement: "User Management",
    helpGuide: "Help Guide",
    helpGuide01: "Help Guide 01",
    helpGuide02: "Help Guide 02",
    profile: "Profile",
  },
  navbar: {
    home: "Home",
    logOut: "Log Out",
    profile: "Profile",
    theme: "Theme",
    size: "Global Size",
    switchLanguageTip: "Switch Language Success",
  },
  login: {
    title: "AssetsLint Platform Login",
    logIn: "Login",
    username: "Username",
    password: "Password",
    capsLocked: "Caps Locked",
    nameTip: "Please enter the correct user name",
    passwordTip: "The password can not be less than 6 digits",
    any: "any",
    thirdparty: "Or connect with",
  },
  home: {
    automatedResourceTesting: "Automated Resource Testing Platform",
    clickToHelpGuide: "Go to help guide",
    demoVideo: "Demo Video",
    platformModules: "Platform Modules",
    myProjects: "My Projects",
    checkAllProject: "View all managed projects",
    rulesList: "Rules List",
    checkAllrules: "View and Modify created rules",
    createRule: "Create Rule",
    createNewRule: "Create new test rule parameters",
    automatedResource: "Automated Testing Of Resources",
    introduction: "Introduction",
    assetsLint: "Resource Testing（AssetsLint）",
    assetsLintText:
      "A complete resource detection solution for the main handheld game engines (unity, UE) on the market. It is mainly divided into three modules: resource scanning, rule editing and front-end display.",
    advantages: "Advantages",
    noCode: "No Code",
    noCodeText:
      "Users do not need to understand the underlying code implementation, through the rule editor can write rules, convenient for art and QA students.",
    ruleCompleteness: "Rule Completeness",
    ruleCompletenessText:
      "Support rules with or without logical nesting operations, realising the conversion of code and rules.",
    visualisation: "Visualisation",
    visualisationText:
      "Scan data visualisation for easy viewing of problem points, and long term data tracking.",
    continuousIntegration: "Continuous Integration",
    continuousIntegrationText:
      "Rules are persisted to the database, enabling continuous integration, incremental scanning, etc. by enabling decoding of rules.",
    ruleEditor: "AssetsLint Rule Editor",
    serialisation: "Serialisation",
    deserialisation: "Deserialisation",
    goCreateRule: "Go to Create Rules",
  },
  project: {
    myProject: "My Projects",
    latestVersion: "Latest Version",
    errorCount: "Error Count",
    lastScanned: "Last Scanned",
    versions: "Versions",
    allVersions: " All Versions",
    uploadReport: "Upload BranchReport Files",
    uploadTips1: "Drag files here, Or",
    uploadTips2: " click to upload",
    uploading: "Uploading",
    upload: "Upload",
    close: "Close",
    fileExists: "This file already exists",
    existed: "existed",
    selectFiles: "Please select files before uploading",
    uploadSuccess: "uploaded successfully!",
    noData: "None",

    addBranch: "Add/Delete Branch",
    branchName: "BranchName",
    enterBranchName: "Enter branch name",
    uploadRuleConfig: "UploadRuleConfig",
    clickSelect: "Click Select",
    addBranchSuccess: "Add branch successfully!",
    branchExists: "Branch already exists!",
    wrongFormat:
      "The format of rule_config.json is wrong, please save it in utf-8!",
    cancel: "Cancel",
    delete: "Delete",
    add: "Add",
    uploadTips: "If you delete the branch, you don't need to upload!",
    isDeleteBranch: "Is delete this branch?",
  },
  projectDetail: {
    backMyProject: "Back To My Projects",
    report: " Report",
    projectDetail: "Project Detail",
    projectName: "Project Name",
    lastScanned: "Latest Scan Time",
    totalResources: "Total Resources",
    totalResourcesSize: "Total Resources Size",
    errorCount: "Error Count",
    resourceComposition: "Resource Composition",
    resourceData: "Resource Data",
    resourceSize: "Resource Size",
    resourceErrorCount: "Resource Error Count",
    resourceType: "Resource Type",
    total: "Total",
    reportList: "Report List",
    isGoRulesList: "Go to Rules List?",
    viewRulesList: "View Rules List",
    compareReports: "Compare Selected Reports",
    selectTwoReport: "Please Select Two Report",
    selectAnotherReport: "Please Select Another Report",
    testId: "Test ID",
    testVersion: "Test Version",
    dateTime: "DateTime",
    searchTestId: "Enter test ID to search",
    accessReport: "Access Report",
    startScan: "Start Scan",
    reportScan: "Report Scan",
    scanProgress: "Scan Progress",
    scanTime: "Scan Time",
    ok: "OK",
    min: "Min",
    second: "Sec",
    hour: "Hour",
    isStartScan: "Is Start Scan?",
  },
  reportDetail: {
    backToProjectReport: "Back To ProjectReport",
    reportDetail: "Report Detail",
    testId: "Test ID",
    scanTime: "Scan Time",
    testVersion: "Test Version",
    errorCount: "Error Count",
    reportList: "Report List",
    filterType: "Filter Type",
    chooseType: "Please choose the type",
    searchKeyWords: "Please enter keywords to search",
    errorList: "Error List",
    operation: "Operation",
    viewMore: "Check More",
    detailedData: "Details Data",
    switchFieldName: "Switch Field Name",
  },
  reportComparison: {
    backToProjectReport: "Back To ProjectReport",
    reportComparison: "Report Comparison",
    testId: "Test ID",
    comparedTestId: " Compared Test ID",
    extraData: " Report extra data",
    missingData: " Report missing data",
  },
  rulesList: {
    rulesList: "Rules List",
    createRule: "Create Rule",
    deleteRule: "Delete Selected Rule",
    filterProject: "Filter Project",
    selectProject: "Select project",
    selectBranch: "Select branch",
    ruleCategories: "Rule Type",
    tableName: "Excel Name",
    selectRuleCategory: "Please select",
    allType: "All",
    selectAll: "Select All",
    goToCreateRule: "Go to create rule page?",
    operateSuccess:"operate successfully"
  },
  ruleEdit: {
    backToRulesList: "Back To Rules List",
    ruleEdit: "Rule Edit",
  },
  ruleCreate: {
    ruleCreate: "Create Rule",
    projectBranch: "Pro&Branch",
    selectProject: "Select project",
    selectBranch: "Select branch",
    missingBranch: "Missing project branch,Please upload!",
    missingRuleConfig: "Missing rule_config!",
    ruleCategories: "Rule Type",
    tableName: "Excel Name",
    switchTypeTips: "Switching will clear all parameters!",
    chooseRuleType: "Please Choose",
    ruleName: "Rule Name",
    enterName: "Enter rule name",
    enabled: "Enabled",
    enable: "Eenable",
    ignore: "Ignore",
    ignored: "Ignored",
    conditionParameter: "Condition Parameter",
    targetParameter: "Target Parameter",
    testRule: "Test Rule",
    importRule: "Import Rule",
    downloadRule: "Download Rule",
    saveRule: "Save Rule",
    projectBranchRequired: "Project&Branch Required",
    ruleCategoriesRequired: "Rule Type Required",
    tableNameRequired: "Excel Required",
    ruleNameRequired: "Rule Name Required",
    tips: "Tips",
    switched: "switched!",
    cancelSwitch: "Cancel Switch!",
    conditionGroupNoConditionTips:
      "The condition group of the condition parameter has no condition added, please set the condition completely!",
    conditionNoOperatorTips:
      "The condition group of the condition parameter has no condition added, please set the condition completely!",
    conditionNoValueTips:
      "The value of the condition parameter is incomplete, please check whether all the fields have been filled in!",
    assertsGroupNoConditionTips:
      "The condition group of the target parameter has no condition added, please set the condition completely!",
    assertsNoOperatorTips:
      "The condition group of the target parameter has no condition added, please set the condition completely!",
    assertsNoValueTips:
      "The value of the target parameter is incomplete, please check whether all the fields have been filled in!",
    requiredNotEmptyTips:
      "Required items cannot be empty, please fill in completely!",
    targetParameterAddConditionTips:
      "Please add at least one condition group to the target parameter!",
    isExportJsonFile: "Do you want to export a file in JSON format?",
    downloadStarted: "Download started!",
    exportCanceled: "Export canceled!",
    isSaveRule: "Is save the rule?",
    saved: "Saved!",
    saveCanceled: "Save canceled!",

    addConditionGroup: "Add Condition Group",
    addCondition: "Add Condition",
    deleteConditionGroup: "Delete Condition Group",
    forward: "Forward",
    reverse: "Reverse",
    SatisfiesRegularity: "Satisfies Reg",
    include: "Include",
    withinRange: "Within Range",
    inSet: "In Set",
    choose: "Choose",
    enterNumber: "Enter Number",
    enterCommaSeparatedValues: "Enter multiple values separated by commas", // New: Support comma-separated input for 'in' operator
    enterCharacters: "Enter Characters",
    insertConditionGroup: "Insert Condition Group",
    isDelCondition: "The condition will be removed, continue?",
    isDelConditionGroup: "The condition Group will be removed, continue?",
    deleted: "Deleted!",
    cancelDelete: "Cancel Delete",

    import: "Import Rule",
    selectProjectBranch: "Must Select Project&Branch",
    uploadTips1: "Drag files here, Or",
    uploadTips2: " click to upload",
    uploadJson: "Upload json file, and only 1 file can be uploaded",
    ok: "OK",
    cancel: "Cancel",
    onlyJsonTips: "Only one json file can be imported at a time!",
    noDataTips: "No data, please re-select the file to upload!",
    tips: "Tips",
    fileRemoved: "File removed!",
    coverageDataTips:
      "The original data will be overwritten after importing, confirm the import?",
    importSucceeded: "Import succeeded!",
    importError:
      "Error! Unable to find the corresponding rule type, please confirm the project and branch!",
  },
  testRule: {
    testRule: "Test Rule",
    errorList: "Error List",
    operate: "Operate",
    checkMore: "Check More",
    dataDetails: "Data Details",
  },
  permissionRole: {
    roleManagement: "Role Management",
    addRole: "Add Role",
    roleId: "Role ID",
    roleKey: "RoleKey",
    roleName: "RoleName",
    roleDescription: "Description",
    operator: "Operate",
    edit: "Edit",
    delete: "Delete",
    editRole: "Edit Role",
    pageList: "Page List",
    enterRoleName: "Enter RoleName",
    enterRoleKey: "Enter RoleKey",
    enterText: "Enter description text",
    ok: "OK",
    cancel: "Cancel",
    characterLength: "3 to 15 characters in length",
    cannotSpecialCharacters: "Cannot contain special characters",
    isDeleteRole: "Delete this permission role?",
    tips: "Tips",
    editSuccessful: "Edit Successful!",
    roleNameExists: "Rolename already exists, please re-enter!",
    roleKeyExists: "RoleKey already exists, please re-enter!",
    addSuccessful: "Add Successful!",
    deleteSuccessful: "Delete Successful!",
    RequiredTips: "Enter information is incomplete/Incorrect,  please confirm!",
  },
  permissionProject: {
    projectManagement: "Project Management",
    addProject: "Add Project",
    projectLogo: "Project Logo",
    projectId: "Project ID",
    projectName: "ProjectName",
    projectDescription: "Description",
    ignorePath: "IgnorePath",
    createTime: "CreateTime",
    editTime: "EeditTime",
    operator: "Operate",
    setUser: "SetUser",
    edit: "Edit",
    delete: "Delete",
    editProject: "Edit Project",
    enterProjectName: "Enter ProjectName",
    add: "Add",
    enterProjectDescription: "Enter Project Description",
    ok: "OK",
    cancel: "Cancel",

    belongingUser: "Belonging User",
    userName: "UserName",
    enterUserName: "Enter UserName",
    search: "Search",
    existingUser: "Existing User",
    createNewUser: "Create New User",
    isGoManagement: "Go to user management page?",
    batchOperation: "Batch Operation",
    nickName: "NickName",
    state: "State",
    normal: "Normal",
    disabled: "Disabled",
    disassociate: "Disassociate",
    isDisassociate: "Is Disassociate?",
    close: "Close",
    comfirmAdd: "Comfirm Add",
    addSuccess: "Add Success",
    cancelled: "Disassociate Success",

    characterLength: "3 to 15 characters in length",
    isDeleteProject: "Delete this Project?",
    tips: "Tips",
    editSuccessful: "Edit Successful!",
    addSuccessful: "Add Successful!",
    deleteSuccessful: "Delete Successful!",
    RequiredTips: "Enter information is incomplete/Incorrect,  please confirm!",
    isJPG:
      "The uploaded avatar picture can only be in JPG/PNG/Webp/Gif format!",
    isLt2M: "The size of the uploaded avatar picture cannot exceed 2MB!",
    existPath: "Path already exists!",
  },
  permissionUser: {
    userManagement: "User Management",
    addUser: "Add User",
    avatar: "Avatar",
    userId: "ID",
    userName: "User Name",
    nickName: "Nick Name",
    userRole: "Role",
    phone: "Phone",
    email: "Email",
    isEnabled: "IsEnabled",
    operator: "Operate",
    edit: "Edit",
    delete: "Delete",
    resetPwd: "ResetPwd",
    editUser: "Edit User",
    enterUserName: "Enter UserName",
    enterNickName: "Enter NickName",
    selectRole: "Select Role",
    enterPhone: "Enter Phone",
    enterEmail: "Enter Email",
    ok: "OK",
    cancel: "Cancel",

    characterLength: "3 to 15 characters in length",
    phoneRegTips: "Phone number format is incorrect",
    emailRegTips: "E-mail format is incorrect",
    isDeleteUser: "Delete this user?",
    tips: "Tips",
    editSuccessful: "Edit Successful!",
    userNameExists: "Username already exists, please re-enter!",
    addSuccessful: "Add Successful!",
    deleteSuccessful: "Delete Successful!",
    enabled: "Enabled",
    disabled: "Disabled",
    RequiredTips: "Enter information is incomplete/Incorrect, please confirm!",
    isJPG:
      "The uploaded avatar picture can only be in JPG/PNG/Webp/Gif format!",
    isLt2M: "The size of the uploaded avatar picture cannot exceed 2MB!",
    isResetPwd: "Reset this user password to 111111?",
    resetSuccessful: "Reset Successful!",
    cancelReset: "Cancel Reset",
  },
  profile: {
    basicInformation: "Basic Information",
    accountBind: "Account Bind",
    personalInformation: "Personal Information",
    userName: "Username / Account",
    nickName: "Nickname",
    submit: "Submit",
    isSubmit: "Is Submit?",
    tips: "Tips",
    submitSuccess: "Submit Success!",
    userNameExists: "Username already exists, please re-enter!",
    cancelled: "Cancelled",
    bindPhone: "Bind Phoen",
    boundPhone: "Bound Phone",
    modify: "Modify",
    bindEmail: "Bind Email",
    boundEmail: "Bound Email",
    modifyPassword: "Modify Password",
    modifyLoginPassword: "Modify Login Password",
    password: "Password",
    enterPassword: "Enter password",
    passwordMinCharacters: "Password minimum 6 characters",
    newPassword: "New Password",
    enterNewPassword: "Enter new password",
    confirmPassword: "Confirm Password",
    enterConfirmPassword: "Enter confirm password",
    ok: "OK",
    cancel: "Cancel",
    modifyPhone: "Modify Phone",
    phone: "Phone",
    enterPhoneNum: "Enter phone number",
    change: "Change",
    email: "Email",
    enterEmail: "Enter email",
    twoPasswordsInconsistent: "The two passwords do not match",
    changeSuccessful: "Change successful!",
    isJPG:
      "The uploaded avatar picture can only be in JPG/PNG/Webp/Gif format!",
    isLt2M: "The size of the uploaded avatar picture cannot exceed 2MB!",
    uploadSuccess: "uploaded successfully!",
  },
  uploadImage: {
    change: "change",
    check: "check",
  },
  backToTop: "Back To Top",
};
