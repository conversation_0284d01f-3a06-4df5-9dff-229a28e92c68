<template>
  <div class="resource-check-container">
    <div class="page-header">
      <h2>资源检查</h2>
    </div>

    <div class="table-container">
      <el-table
        :data="resourceCheckData"
        border
        style="width: 100%"
        :header-cell-style="{
          background: '#F7FBFF',
          height: '52px',
        }"
      >
        <el-table-column
          prop="function"
          label="功能"
          width="150"
          align="center"
        />
        <el-table-column
          prop="description"
          label="功能介绍"
          min-width="200"
        />
        <el-table-column
          prop="effectiveBranch"
          label="生效分支"
          width="150"
          align="center"
        />
        <el-table-column
          prop="isEnabled"
          label="是否启用"
          width="120"
          align="center"
        >
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.isEnabled"
              @change="handleSwitchChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="100"
          align="center"
        >
          <template slot-scope="scope">
            <el-button
              type="primary"
              size="mini"
              @click="handleConfig(scope.row)"
            >
              配置
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ResourceCheckIndex',
  data() {
    return {
      projectId: '',
      resourceCheckData: [
        {
          id: 1,
          function: '定时检查',
          description: '',
          effectiveBranch: '',
          isEnabled: true
        },
        {
          id: 2,
          function: '白名单路径',
          description: '',
          effectiveBranch: '',
          isEnabled: false
        },
        {
          id: 3,
          function: '黑名单路径',
          description: '',
          effectiveBranch: '',
          isEnabled: false
        },
        {
          id: 4,
          function: '合规配置',
          description: '',
          effectiveBranch: '',
          isEnabled: false
        }
      ]
    };
  },
  created() {
    // 从路由参数中获取projectId
    this.projectId = this.$route.params.projectId;
    this.initializeData();
  },
  watch: {
    '$route'(to, from) {
      // 监听路由变化，更新projectId
      if (to.params.projectId !== from.params.projectId) {
        this.projectId = to.params.projectId;
        this.initializeData();
      }
    }
  },
  methods: {
    initializeData() {
      // TODO: 根据projectId获取检查数据
      console.log('初始化资源检查数据，项目ID:', this.projectId);
      // 这里将来替换为真实的API调用
      // try {
      //   const res = await getResourceCheckData({ projectId: this.projectId });
      //   this.resourceCheckData = res.data;
      // } catch (error) {
      //   console.error('获取资源检查数据失败:', error);
      // }
    },

    handleConfig(row) {
      // 处理配置按钮点击事件
      console.log('配置功能:', row.function);
      // TODO: 实现配置功能的逻辑
      this.$message({
        message: `配置 ${row.function} 功能`,
        type: 'info'
      });
    },

    handleSwitchChange(row) {
      // 处理开关状态变化
      console.log(`${row.function} 功能状态变更为:`, row.isEnabled);

      // TODO: 这里可以调用API更新状态
      // try {
      //   await updateResourceStatus({
      //     projectId: this.projectId,
      //     functionId: row.id,
      //     isEnabled: row.isEnabled
      //   });
      //   this.$message({
      //     message: `${row.function} 功能已${row.isEnabled ? '启用' : '禁用'}`,
      //     type: 'success'
      //   });
      // } catch (error) {
      //   // 如果API调用失败，恢复原状态
      //   row.isEnabled = !row.isEnabled;
      //   this.$message({
      //     message: '状态更新失败，请重试',
      //     type: 'error'
      //   });
      // }

      // 临时提示信息
      this.$message({
        message: `${row.function} 功能已${row.isEnabled ? '启用' : '禁用'}`,
        type: 'success'
      });
    }
  }
};
</script>

<style scoped lang="scss">
.resource-check-container {
  padding: 20px;
  background-color: #fff;
  min-height: calc(100vh - 84px);

  .page-header {
    margin-bottom: 20px;

    h2 {
      margin: 0;
      color: #303133;
      font-size: 20px;
      font-weight: 500;
    }
  }

  .table-container {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    ::v-deep .el-table {
      .el-table__header-wrapper {
        th {
          font-weight: 600;
          color: #606266;
        }
      }

      .el-table__body-wrapper {
        &::-webkit-scrollbar {
          height: 6px;
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background-color: #fff;
        }

        &::-webkit-scrollbar-thumb {
          background-color: #e6e9ed;
          border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb:hover {
          background-color: #d5d8db;
        }
      }
    }
  }
}
</style>
