import request from "@/utils/request";
import Qs from "qs";

// 获取用户所属项目列表
export function getUserProjectList(query) {
  return request({
    url: "/get_user_projectList",
    method: "get",
    params: query,
  });
}
// 获取项目所有分支
export function getProjectBranchs(query) {
  return request({
    url: "/get_project_branchs",
    method: "get",
    params: query,
  });
}
// 获取表格检查分支详情
export function getTableBranchDetail(query) {
  return request({
    url: "/get_table_branch_detail",
    method: "get",
    params: query,
  });
}
// 获取报告基本详细
export function getReportStat(query) {
  return request({
    url: "/get_report_stat",
    method: "get",
    params: query,
  });
}
// 获取美术资源报告列表
export function getReportSummary(query) {
  return request({
    url: "/get_report_summary",
    method: "get",
    params: query,
  });
}
// 获取表格资源报告列表（预留接口，后端还没给到）
export function getTableReportSummary(query) {
  return request({
    url: "/get_table_report_summary",
    method: "get",
    params: query,
  });
}
// 获取报告详细
export function getReportDetail(query) {
  return request({
    url: "/get_report_detail",
    method: "get",
    params: query,
  });
}
// 获取按报错类型分类数据
export function getReportDetailByError(query) {
  return request({
    url: "/get_report_by_rule",
    method: "get",
    params: query,
  });
}
// 删除报告
export function deleteReport(data) {
  data = Qs.stringify(data);
  return request({
    url: "/delete_report",
    method: "post",
    data,
  });
}
// 获取报告对比详细
export function getReportCompare(query) {
  return request({
    url: "/get_report_compare",
    method: "get",
    params: query,
  });
}
// 美术资源添加扫描
export function addTask(query) {
  return request({
    // url: '/add_task',
    url: "/api/add_task",
    method: "get",
    params: query,
  });
}
// 表格检测添加扫描
export function startScanExcel(query) {
  return request({
    url: "/start_scan_excel",
    method: "get",
    params: query,
  });
}
// 获取扫描结果
export function getScanResult(query) {
  return request({
    // url: '/result',
    url: "/api/result",
    method: "get",
    params: query,
  });
}
// 全量检查表格
export function fullScanExcel(query) {
  return request({
    url: "/start_scan_table_excel",
    method: "get",
    params: query,
  });
}
// 获取表格检查结果
export function getTableCheckResult(query) {
  return request({
    url: "/get_table_check_result",
    method: "get",
    params: query,
  });
}

// 创建美术资源项目分支
export function createBranch(data) {
  return request({
    url: `/create_branch`,
    method: "post",
    data,
    headers: { "Content-Type": "multipart/form-data" },
  });
}
// 编辑分支
export function editBranch(data) {
  return request({
    url: `/update_branch`,
    method: "post",
    data,
    headers: { "Content-Type": "multipart/form-data" },
  });
}
// 下载查看上传rules_config文件
export function downloadRuleConfigFile(query) {
  return request({
    url: `/download_rule_config`,
    method: "get",
    params: query,
    responseType: 'blob'  // 指定响应类型为blob以处理文件流
  });
}
// 创建表格检测项目分支
export function createTableProjectBranch(data) {
  data = Qs.stringify(data);
  return request({
    url: `/create_branch`,
    method: "post",
    data,
  });
}
// 删除项目分支
export function deleteBranch(data) {
  data = Qs.stringify(data);
  return request({
    url: `/delete_branch`,
    method: "post",
    data,
  });
}
// 导入已有项目分支

export function importBranch(data) {
  data = Qs.stringify(data);
  return request({
    url: `/import_branch`,
    method: "post",
    data,
  });
}

// 编辑表格资源获取git
export function editGit(data) {
  data = Qs.stringify(data);
  return request({
    url: `/edit_git`,
    method: "post",
    data,
  });
}
// 拉取表格资源
export function pullGit(query) {
  return request({
    url: `/pull_git`,
    method: "get",
    params: query,
  });
}

// 表格检查关联仓库
export function addTableRpeo(data) {
  data = Qs.stringify(data);
  return request({
    url: `/repository/create`,
    method: "post",
    data,
  });
}
// 表格检查更新关联仓库
export function updateTableRpeo(data) {
  data = Qs.stringify(data);
  return request({
    url: `/repository/update`,
    method: "post",
    data,
  });
}
// 表格检查删除关联仓库
export function delTableRpeo(data) {
  data = Qs.stringify(data);
  return request({
    url: `/repository/delete`,
    method: "post",
    data,
  });
}
 // 获取项目详情信息
 export function getProjectInfo(query) {
  return request({
    url: `/get_project`,
    method: "get",
    params: query,
  });
}
