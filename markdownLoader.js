const markdown = require('markdown-it')
// const hljs = require('highlight.js')
// const Prism = require("prismjs");



module.exports = function (src) {

  const html = markdown().render(src)

  return (
    `<template>\n` +
    `<div class="markdown-body">${html}</div>\n` +
    `</template>\n` +

    `<script>
    import Prism from "prismjs";
    
    export default {
      mounted () {
        Prism.highlightAll()
      },
    }
    </script>`+

    `<style scoped src="@/styles/markdown.css">`
  )
}