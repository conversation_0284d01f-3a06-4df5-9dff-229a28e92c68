<template>
  <div class="app-container">
    <div class="report-detail">
      <el-page-header
        :content="'规则详细报告'"
        :title="'返回规则列表'"
        @back="$router.back()"
      />
      <el-divider />
      <div>
        <el-descriptions
          :title="$t('reportDetail.reportDetail')"
          :column="2"
          style="font-size: 16px"
        >
          <el-descriptions-item :label="'规则ID'">{{
            reportDetail.rule_id
          }}</el-descriptions-item>
          <el-descriptions-item :label="'规则名称'">{{
            reportDetail.rule_name
          }}</el-descriptions-item>
          <el-descriptions-item :label="'分支'">{{
            reportDetail.branch
          }}</el-descriptions-item>
          <el-descriptions-item :label="'检查时间'">{{
            reportDetail.scan_time
          }}</el-descriptions-item>
          <el-descriptions-item :label="'生效次数'">{{
            reportDetail.effective_count
          }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <el-table
        :data="reportDetail.scan_error"
        border
        style="width: 100%; margin-top: 20px"
      >
        <el-table-column prop="trigger_time" label="触发时间" />
        <el-table-column prop="trigger_method" label="触发形式" />
        <el-table-column prop="user" label="触发人" />
        <el-table-column prop="branch" label="触发分支" />
        <el-table-column label="错误内容" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.exception_result">{{
              scope.row.exception_result + "\n"
            }}</span>
            <span v-if="Object.keys(scope.row.error_report).length !== 0">{{
              JSON.stringify(scope.row.error_report)
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="120" align="center">
          <template slot-scope="scope">
            <el-link
              type="primary"
              :underline="false"
              @click="checkMore(scope.row)"
              >查看更多</el-link
            >
          </template>
        </el-table-column>
      </el-table>
      <div style="display: flex; justify-content: end">
        <Pagination
          style="margin-top: 10px; padding: 10px 20px"
          :total="total"
          :page.sync="listQuery.page"
          :limit.sync="listQuery.limit"
          :auto-scroll="listQuery.limit >= 30"
          @pagination="pageSwitch"
        />
      </div>
    </div>
    <!-- 查看更多弹窗 -->
    <el-dialog title="错误信息" :visible.sync="dialogVisible" width="30%">
      <el-descriptions direction="vertical" :column="1" border>
        <el-descriptions-item
          v-if="errorInfo.exception_result"
          label="exception_result"
          >{{ errorInfo.exception_result }}</el-descriptions-item
        >
        <el-descriptions-item
          v-if="Object.keys(errorInfo.error_report).length !== 0"
          label="error_report"
        >
          <span>{{ JSON.stringify(errorInfo.error_report) }}</span>
        </el-descriptions-item>
      </el-descriptions>
      <span
        v-if="
          !errorInfo.exception_result &&
          Object.keys(errorInfo.error_report).length === 0
        "
        >暂无信息</span
      >
      <span slot="footer">
        <!-- <el-button @click="dialogVisible = false">取 消</el-button> -->
        <el-button type="primary" @click="dialogVisible = false"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <!-- 返回顶部组件 -->
    <el-tooltip placement="top" :content="$t('backToTop')">
      <back-to-top
        :visibility-height="300"
        :back-position="50"
        transition-name="fade"
      />
    </el-tooltip>
  </div>
</template>

<script>
import { getExcelRuleDetail } from "@/api/rule";
import BackToTop from "@/components/BackToTop";
import Pagination from "@/components/Pagination/index.vue";

export default {
  components: { BackToTop, Pagination },
  data() {
    return {
      reportDetail: {},
      listQuery: {
        page: 1,
        limit: 20,
      },
      total: 0,
      dialogVisible: false,
      errorInfo: { exception_result: "", error_report: {} },
    };
  },
  mounted() {
    this.getRuleDetail();
  },

  methods: {
    // 获取报告详情
    async getRuleDetail() {
      let id = this.$route.query.reportId;
      if (!id) {
        this.$router.push("/tabledetect/rulelist");
      }
      let params = {
        rule_id: id,
        pageNum: this.listQuery.page,
        pageSize: this.listQuery.limit,
      };
      let { code, data, msg } = await getExcelRuleDetail(params);
      if (code === 200) {
        this.reportDetail = data;
        this.total = data.effective_count;
      }
    },
    // 分页器页码切换 获取页码和页数
    pageSwitch(val) {
      this.getRuleDetail();
    },
    // 查看更多错误信息
    checkMore(row) {
      // console.log(row);
      this.errorInfo = row;
      this.dialogVisible = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  .report-detail {
    background-color: #fff;
    padding: 15px;
    padding-top: 25px;
  }
}
</style>
