<template>
  <div class="app-container">
    <div class="app-content">
      <div class="el-page-header">
        <div class="el-page-header__content">
          {{ $t("permissionRole.roleManagement") }}
        </div>
      </div>
      <el-divider />
      <el-button type="primary" icon="el-icon-plus" @click="handleAddRole">{{
        $t("permissionRole.addRole")
      }}</el-button>

      <el-table
        :data="rolesList"
        style="width: 100%; margin-top: 30px"
        :header-cell-style="{
          background: '#F7FBFF',
          height: '52px',
        }"
      >
        <!-- <el-table-column align="center" label="角色Key" width="220">
        <template slot-scope="scope">
          {{ scope.row.key }}
        </template>
      </el-table-column> -->
        <el-table-column :label="$t('permissionRole.roleId')"><template slot-scope="scope">
          {{ scope.row.roleId }}
        </template></el-table-column>
        <el-table-column :label="$t('permissionRole.roleName')">
          <template slot-scope="scope">
            {{ scope.row.roleName }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('permissionRole.roleKey')"><template slot-scope="scope">
          {{ scope.row.roleKey }}
        </template></el-table-column>

        <el-table-column :label="$t('permissionRole.roleDescription')">
          <template slot-scope="scope">
            {{ scope.row.description }}
          </template>
        </el-table-column>
        <el-table-column align="center" :label="$t('permissionRole.operator')" fixed="right" width="180">
          <template slot-scope="scope">
            <el-button type="text" icon="el-icon-edit-outline" @click="handleEdit(scope)">{{ $t("permissionRole.edit")
            }}</el-button>
            <el-button
              type="text"
              :style="{
                color: scope.row.roleKey === 'admin' ? '#c0c4cc' : '#f56c6c',
              }"
              icon="el-icon-delete"
              :disabled="scope.row.roleKey === 'admin'"
              @click="handleDelete(scope.row.roleId)"
            >{{ $t("permissionRole.delete") }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页器 -->
      <pagination
        style="margin-top: 0; padding: 10px 20px"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="pageSwitch"
      />
    </div>

    <el-dialog
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      :title="dialogType === 'edit'
        ? $t('permissionRole.editRole')
        : $t('permissionRole.addRole')
      "
      @close="closeRoleDialog"
    >
      <el-form
        ref="roleForm"
        :model="role"
        :rules="rules"
        label-width="100px"
        label-position="right"
        @close="closeRoleDialog"
      >
        <el-form-item :label="$t('permissionRole.roleName')" prop="roleName">
          <el-input ref="roleName" v-model="role.roleName" :placeholder="$t('permissionRole.enterRoleName')" />
        </el-form-item>
        <el-form-item :label="$t('permissionRole.roleKey')" prop="roleKey">
          <el-input
            ref="roleKey"
            v-model="role.roleKey"
            :placeholder="$t('permissionRole.enterRoleKey')"
            :disabled="role.isDisabled"
          />
        </el-form-item>
        <el-form-item :label="$t('permissionRole.roleDescription')">
          <el-input
            v-model="role.description"
            :autosize="{ minRows: 2, maxRows: 4 }"
            type="textarea"
            maxlength="32"
            :placeholder="$t('permissionRole.enterText')"
            show-word-limit
          />
        </el-form-item>
        <el-form-item :label="$t('permissionRole.pageList')">
          <el-tree
            ref="tree"
            :check-strictly="checkStrictly"
            :data="routesData"
            :props="defaultProps"
            :default-expand-all="true"
            show-checkbox
            node-key="path"
            class="permission-tree"
          />
        </el-form-item>
      </el-form>
      <div style="text-align: right">
        <el-button type="danger" @click="closeRoleDialog">{{
          $t("permissionRole.cancel")
        }}</el-button>
        <el-button type="primary" @click="confirmRole">{{
          $t("permissionRole.ok")
        }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination/index.vue'
import path from 'path'
import { deepClone } from '@/utils'
const { asyncRoutes } = require('./mixins/routes')
import {
  // getRoutes,
  getRoles,
  addRole,
  deleteRole,
  updateRole
} from '@/api/role'

const defaultRole = {
  roleKey: '',
  roleName: '',
  description: '',
  disabled: true,
  routes: []
}
import i18n from '@/lang'

export default {
  components: { Pagination },
  data() {
    return {
      role: Object.assign({}, defaultRole),

      routes: [],
      rolesList: [],
      dialogVisible: false,
      dialogType: 'new',
      checkStrictly: false,
      defaultProps: {
        children: 'children',
        label: 'title',
        disabled: 'disabled'
      },

      rules: {
        roleName: [
          {
            required: true,
            message: this.$t('permissionRole.enterRoleName'),
            trigger: 'blur'
          },
          {
            min: 3,
            max: 15,
            message: this.$t('permissionRole.characterLength'),
            trigger: 'blur'
          }
        ],
        roleKey: [
          {
            required: true,
            message: this.$t('permissionRole.enterRoleKey'),
            trigger: 'blur'
          },
          {
            min: 3,
            max: 15,
            message: this.$t('permissionRole.characterLength'),
            trigger: 'blur'
          },
          {
            pattern: /^[a-zA-Z0-9_]{0,}$/,
            message: this.$t('permissionRole.cannotSpecialCharacters'),
            trigger: 'blur'
          }
        ]
      },

      total: 0,
      listQuery: {
        page: 1,
        limit: 10
      }
    }
  },
  computed: {
    routesData() {
      return this.routes
    }
  },
  created() {
    // get all routes and roles list
    this.getRoutes()
    this.getRoles()
  },
  methods: {
    getRoutes() {
      this.serviceRoutes = asyncRoutes
      const routes = this.generateRoutes(asyncRoutes)
      this.routes = this.i18n(routes)
    },
    i18n(routes) {
      const app = routes.map((route) => {
        route.title = i18n.t(`route.${route.title}`)
        if (route.children) {
          route.children = this.i18n(route.children)
        }
        return route
      })
      return app
    },

    // 获取所有角色数据
    async getRoles() {
      const params = {
        pageNum: this.listQuery.page,
        pageSize: this.listQuery.limit
      }
      const { code, roleList, dataLen } = await getRoles(params)
      if (code === 200) {
        this.rolesList = roleList
        this.total = dataLen
      }
    },
    // 分页器页码改变获取角色数据
    pageSwitch(value) {
      this.getRoles()
    },

    // 调整路由结构，使其看起来与侧边栏相同
    generateRoutes(routes, basePath = '/') {
      const res = []

      for (let route of routes) {
        // 跳过一些路由
        if (route.hidden) {
          continue
        }

        const onlyOneShowingChild = this.onlyOneShowingChild(
          route.children,
          route
        )

        if (route.children && onlyOneShowingChild && !route.alwaysShow) {
          route = onlyOneShowingChild
        }

        const data = {
          path: path.resolve(basePath, route.path),
          title: route.meta && route.meta.title
        }

        // recursive child routes
        if (route.children) {
          data.children = this.generateRoutes(route.children, data.path)
        }
        res.push(data)
      }
      return res
    },
    // reference: src/view/layout/components/Sidebar/SidebarItem.vue
    onlyOneShowingChild(children = [], parent) {
      let onlyOneChild = null
      const showingChildren = children.filter((item) => !item.hidden)

      // When there is only one child route, the child route is displayed by default
      if (showingChildren.length === 1) {
        onlyOneChild = showingChildren[0]
        onlyOneChild.path = path.resolve(parent.path, onlyOneChild.path)
        return onlyOneChild
      }

      // Show parent if there are no child route to display
      if (showingChildren.length === 0) {
        onlyOneChild = { ...parent, path: '', noShowingChildren: true }
        return onlyOneChild
      }

      return false
    },

    // 比对路由权限
    generateArr(routes) {
      let data = []
      routes.forEach((route) => {
        data.push(route)
        if (route.children) {
          const temp = this.generateArr(route.children)
          if (temp.length > 0) {
            data = [...data, ...temp]
          }
        }
      })
      return data
    },
    // 新增角色
    handleAddRole() {
      const routes = this.generateRoutes(asyncRoutes)
      this.routes = this.i18n(routes)
      this.role = Object.assign({}, defaultRole)
      if (this.$refs.tree) {
        this.$refs.tree.setCheckedNodes([])
      }
      this.dialogType = 'new'
      this.dialogVisible = true
    },
    // 禁用页面列表选项
    isDisabled(routes) {
      for (const route of routes) {
        if (
          route.path === "/permission/role" ||
          route.path === "/permission/users"
        )
          route.disabled = true;
        if (route.children && route.children.length > 0) {
          this.isDisabled(route.children)
        }
      }
    },
    // 编辑角色
    handleEdit(scope) {
      this.dialogType = 'edit'
      this.dialogVisible = true
      this.checkStrictly = true
      this.role = {}
      this.role = deepClone(scope.row)

      if (this.role.roleKey === 'admin') {
        this.isDisabled(this.routes)
        this.role.isDisabled = true
      } else {
        const routes = this.generateRoutes(asyncRoutes)
        this.routes = this.i18n(routes)
        this.role.isDisabled = false
      }
      this.$nextTick(() => {
        const routes = this.generateRoutes(this.role.routes)
        this.$refs.tree.setCheckedNodes(this.generateArr(routes))
        // 设置节点的检查状态不影响其父节点和子节点
        this.checkStrictly = false
      })
    },
    // 删除角色
    handleDelete(roleId) {
      this.$confirm(
        this.$t('permissionRole.isDeleteRole'),
        this.$t('permissionRole.tips'),
        {
          type: 'warning'
        }
      )
        .then(async() => {
          await deleteRole({ roleId })
          // this.rolesList.splice($index, 1);
          this.getRoles()
          this.$message({
            type: 'success',
            message: this.$t('permissionRole.deleteSuccessful')
          })
        })
        .catch((err) => {
          console.error(err)
        })
    },
    // 生成树形结构
    generateTree(routes, basePath = '/', checkedKeys) {
      const res = []

      for (const route of routes) {
        const routePath = path.resolve(basePath, route.path)

        // 递归子路由
        if (route.children) {
          route.children = this.generateTree(
            route.children,
            routePath,
            checkedKeys
          )
        }
        if (
          checkedKeys.includes(routePath) ||
          (route.children && route.children.length >= 1)
        ) {
          res.push(route)
        }
      }
      return res
    },

    // 角色编辑/新增取消按钮
    closeRoleDialog() {
      this.role = {}
      this.$refs.roleForm.resetFields()
      this.dialogVisible = false
    },
    // 角色编辑/新增确认按钮
    async confirmRole() {
      const isEdit = this.dialogType === 'edit'
      this.$refs.roleForm.validate(async(valid) => {
        if (valid) {
          // 获取选中项
          const checkedKeys = this.$refs.tree.getCheckedKeys()
          // 调用生成树形结构的函数 保存更新后的选中项
          this.role.routes = this.generateTree(
            deepClone(this.serviceRoutes),
            '/',
            checkedKeys
          )

          if (isEdit) {
            const params = this.role
            params.routes.forEach((item) => {
              if (item.children) {
                item.children.forEach((child) => {
                  child.path = child.path.split('/').at(-1)
                })
              }
            })
            params.routes = JSON.stringify(params.routes)
            const { code, msg } = await updateRole(params)
            if (code === 200) {
              this.$message({
                type: 'success',
                message: this.$t('permissionRole.editSuccessful')
              })
              this.getRoles()
              this.closeRoleDialog()
              return
            }
            if (code === 215) {
              this.$message({
                type: 'error',
                message: this.$t('permissionRole.roleNameExists')
              })
              this.$refs.roleName.focus()
              return
            }
            if (code === 216) {
              this.$message({
                type: 'error',
                message: this.$t('permissionRole.roleKeyExists')
              })
              this.$refs.roleKey.focus()
            }
          } else {
            const params = {
              roleName: this.role.roleName,
              roleKey: this.role.roleKey,
              description: this.role.description,
              routes: this.role.routes
            }
            params.routes.forEach((item) => {
              if (item.children) {
                item.children.forEach((child) => {
                  child.path = child.path.split('/').at(-1)
                })
              }
            })
            params.routes = JSON.stringify(params.routes)
            const { code, msg } = await addRole(params)
            if (code === 200) {
              this.$message({
                type: 'success',
                message: this.$t('permissionRole.addSuccessful')
              })
              this.getRoles()
              this.closeRoleDialog()
              return
            }
            if (code === 215) {
              this.$message({
                type: 'error',
                message: this.$t('permissionRole.roleNameExists')
              })
              this.$refs.roleName.focus()
              return
            }
            if (code === 216) {
              this.$message({
                type: 'error',
                message: this.$t('permissionRole.roleKeyExists')
              })
              this.$refs.roleKey.focus()
            }
          }
        } else {
          this.$message.warning(this.$t('permissionRole.RequiredTips'))
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
// 修改elementui表格的默认样式
::v-deep .el-table__body-wrapper {
  cursor: pointer;

  &::-webkit-scrollbar {
    height: 6px;
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    // 轨道颜色
    background-color: #fff;
  }

  &::-webkit-scrollbar-thumb {
    // 滚动块颜色
    background-color: #e6e9ed;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    // hover高亮
    background-color: #d5d8db;
  }
}

// 当表格没有滚动条时
::v-deep .el-table__body-wrapper.is-scrolling-none ~ .el-table__fixed-right {
  height: 100% !important;
  bottom: 0 !important;
  box-shadow: none !important;
}

// 当表格有纵向滚动条时
::v-deep .el-table--scrollable-y .el-table__fixed-right {
  right: 6px !important;
}

// 当表格只有横向滚动条，没有纵向滚动条时
::v-deep
  .el-table--scrollable-x:not(.el-table--scrollable-y)
  .el-table__fixed-right {
  right: 0 !important;
}

// 处理有x滚动条时高度遮挡滚动条
::v-deep .el-table--scrollable-x {
  .el-table__fixed,
  .el-table__fixed-right {
    height: calc(100% - 6px) !important;
  }
}

// 存在滚动条的时候在right
::v-deep .el-table--scrollable-y {
  .el-table__fixed-right {
    right: 6px !important;
  }
}

// 解决修改滚动条底部不对齐问题
::v-deep .el-scrollbar__wrap::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.app-container {
  background-color: #f6f8f9;

  .permission-tree {
    margin-bottom: 30px;
  }
}
</style>
