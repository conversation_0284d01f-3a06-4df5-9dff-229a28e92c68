<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="`【${projectUserData.projectName}】${$t(
        'permissionProject.belongingUser'
      )}`"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      @close="closeDialog"
      width="60%"
    >
      <div class="search">
        <div>{{ $t("permissionProject.userName") }}：</div>
        <el-input
          v-model="outSearchKey"
          :placeholder="$t('permissionProject.enterUserName')"
          clearable
          size="small"
          @keyup.enter.native="searchProjectUser"
          @clear="searchProjectUser"
        />
        <el-button
          type="primary"
          icon="el-icon-search"
          size="small"
          @click="searchProjectUser"
          >{{ $t("permissionProject.search") }}</el-button
        >
      </div>
      <el-button type="primary" icon="el-icon-plus" @click="handleAddUser">{{
        $t("permissionProject.existingUser")
      }}</el-button>
      <!-- <el-button type="primary" @click="goCreateUser">创建新用户</el-button> -->
      <el-popconfirm
        :title="$t('permissionProject.isGoManagement')"
        placement="top-start"
        @confirm="goCreateUser"
      >
        <el-button
          v-if="isUserManagementPermissions()"
          slot="reference"
          type="primary"
          style="margin-left: 15px"
          >{{ $t("permissionProject.createNewUser") }}</el-button
        >
      </el-popconfirm>
      <el-dropdown v-if="userIds.length > 0" @command="removeSelectUser">
        <el-button style="margin-left: 15px">
          {{ $t("permissionProject.batchOperation")
          }}<i class="el-icon-arrow-down el-icon--right" />
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item
            icon="el-icon-magic-stick"
            command="removeSelectUser"
            >{{ $t("permissionProject.disassociate") }}</el-dropdown-item
          >
        </el-dropdown-menu>
      </el-dropdown>
      <el-table
        ref="multipleTable"
        :data="projectUserList"
        border
        tooltip-effect="dark"
        style="width: 100%; margin-top: 20px"
        max-height="500"
        :header-cell-style="{
          background: '#F7FBFF',
          height: '52px',
        }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column
          prop="userName"
          :label="$t('permissionProject.userName')"
        />
        <el-table-column
          prop="nickName"
          :label="$t('permissionProject.nickName')"
        />
        <!-- <el-table-column prop="roleName" label="用户角色" /> -->
        <el-table-column :label="$t('permissionProject.state')">
          <template slot-scope="scope">
            {{
              scope.row.isEnabled
                ? $t("permissionProject.normal")
                : $t("permissionProject.disabled")
            }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('permissionProject.operator')">
          <!-- <el-button type="text" icon="el-icon-edit-outline">编辑</el-button> -->
          <!-- <el-button type="text" icon="el-icon-magic-stick">取消关联</el-button> -->
          <template slot-scope="scope">
            <el-popconfirm
              :title="$t('permissionProject.isDisassociate')"
              placement="top-start"
              @confirm="removeUser(scope.row.userId)"
            >
              <el-button
                slot="reference"
                type="text"
                icon="el-icon-magic-stick"
                >{{ $t("permissionProject.disassociate") }}</el-button
              >
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">{{
          $t("permissionProject.close")
        }}</el-button>
        <!-- <el-button type="primary" @click="closeDialog"
          >确 认</el-button
        > -->
      </div>
    </el-dialog>
    <el-dialog
      width="65%"
      :title="$t('permissionProject.existingUser')"
      :visible.sync="innerVisible"
      append-to-body
    >
      <div class="search">
        <div>{{ $t("permissionProject.userName") }}：</div>
        <el-input
          v-model="innerSearchKey"
          :placeholder="$t('permissionProject.enterUserName')"
          clearable
          size="small"
          @keyup.enter.native="innerSearchUser"
          @clear="innerSearchUser"
        />
        <el-button
          type="primary"
          icon="el-icon-search"
          size="small"
          @click="innerSearchUser"
          >{{ $t("permissionProject.search") }}</el-button
        >
      </div>
      <el-table
        ref="multipleTable"
        :data="allUserData"
        border
        style="width: 100%; margin-top: 20px"
        max-height="500"
        tooltip-effect="dark"
        :header-cell-style="{
          background: '#F7FBFF',
        }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column
          prop="userName"
          :label="$t('permissionProject.userName')"
        />
        <el-table-column
          prop="nickName"
          :label="$t('permissionProject.nickName')"
        />
        <!-- <el-table-column prop="roleName" label="用户角色" /> -->
        <!-- <el-table-column prop="phone" label="手机" />
        <el-table-column prop="email" label="邮箱" /> -->
        <el-table-column :label="$t('permissionProject.state')">
          <template slot-scope="scope">
            {{
              scope.row.isEnabled
                ? $t("permissionProject.normal")
                : $t("permissionProject.disabled")
            }}
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="innerClose">{{
          $t("permissionProject.close")
        }}</el-button>
        <el-button type="primary" @click="confirmUserProject">{{
          $t("permissionProject.comfirmAdd")
        }}</el-button>
      </div>
      <pagination
        style="margin-top: 0; padding: 10px 20px"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="pageSwitch"
      />
    </el-dialog>
  </div>
</template>

<script>
import elDragDialog from "@/directive/el-drag-dialog";
import Pagination from "@/components/Pagination/index.vue";
import { getAllUser, getUserProjectList } from "@/api/permission-users";
import { projectAddUser, projectDeleteUser } from "@/api/permission-project";

export default {
  directives: { elDragDialog },
  components: { Pagination },
  props: ["setUserModal", "projectUserData", "closeDialog"],
  data() {
    return {
      dialogVisible: false,
      outSearchKey: "", //外部弹窗的搜索关键词
      usersList: [], //保存接口获取的项目所有的用户数据
      projectUserList: [], //项目所有的用户表格展示的数据
      userIds: [],

      innerSearchKey: "", // 内部弹窗搜索关键词
      innerVisible: false,
      allUserList: [], //保存接口获取的所有的用户数据
      allUserData: [], //所有的用户表格展示的数据
      total: 0,
      listQuery: {
        page: 1,
        limit: 10,
      },

      userRoutes: [], //保存用户路由
    };
  },
  computed: {
    permission_routes() {
      return this.$store.getters.permission_routes; //系统语言
    },
  },
  watch: {
    setUserModal(val) {
      this.dialogVisible = val;
    },
    projectUserData(val) {
      this.getProjectUser(val.projectId);
    },
  },
  methods: {
    // 判断是否有跳转用户管理页面的按钮权限
    isUserManagementPermissions() {
      let routes = [];
      this.permission_routes.forEach((item) => {
        if (item.path === "/permission") {
          routes = item.children;
          return;
        }
      });
      return routes.some((item) => item.path === "users");
    },
    // 获取项目所属的用户
    async getProjectUser(projectId) {
      // 传项目id获取
      let { code, data } = await getUserProjectList({ projectId });
      if (code === 200) {
        this.usersList = data;
        this.projectUserList = data;
      }
    },
    //搜索项目所属的用户
    searchProjectUser() {
      let filterData = this.usersList.filter(
        (data) =>
          !this.outSearchKey ||
          data.userName.toLowerCase().includes(this.outSearchKey.toLowerCase())
      );
      this.projectUserList = filterData; //保存过滤后的项目所属用户的表格数据
    },

    // 获取所有用户列表
    async getUsersList() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      };
      const { code, data, dataLen } = await getAllUser(params);
      if (code === 200) {
        this.allUserList = data.filter(
          (item) => !this.usersList.some((ele) => ele.userId === item.userId)
        );
        // this.allUserList = data;
        this.total = this.allUserList.length;
        this.allUserData = this.allUserList.slice(
          this.listQuery.limit * (this.listQuery.page - 1),
          this.listQuery.limit * this.listQuery.page
        );
      }
    },
    // 已有用户的弹窗的搜索
    innerSearchUser() {
      let allUserfilterData = this.allUserList.filter(
        (data) =>
          !this.innerSearchKey ||
          data.userName
            .toLowerCase()
            .includes(this.innerSearchKey.toLowerCase())
      );
      this.allUserData = allUserfilterData.slice(
        this.listQuery.limit * (this.listQuery.page - 1),
        this.listQuery.limit * this.listQuery.page
      ); //保存过滤后的表格数据
      this.total = allUserfilterData.length;
    },
    // 分页器页码改变获取数据
    pageSwitch(value) {
      // console.log(value.page, value.limit);
      if (!this.innerSearchKey) {
        this.allUserData = this.allUserList.slice(
          value.limit * (value.page - 1),
          value.limit * value.page
        );
      } else {
        this.innerSearchUser();
      }
    },

    // 获取选中的用户
    handleSelectionChange(val) {
      this.userIds = [];
      val.forEach((item) => {
        this.userIds.push(item.userId);
      });
    },
    // 确认添加选择用户
    async confirmUserProject() {
      // 传选中的用户id(支持多个)和项目id发起移除关联请求
      let params = {
        projectId: this.projectUserData.projectId,
        userId: this.userIds.join(),
      };
      let { code, msg } = await projectAddUser(params);
      if (code === 200) {
        this.$message.success(this.$t("permissionProject.addSuccess"));
        this.getProjectUser(params.projectId);
        this.innerClose();
      }
    },
    // 关闭添加已有用户弹窗
    innerClose() {
      this.userIds = [];
      this.innerVisible = false;
      this.innerSearchKey = "";
    },

    // 添加已有用户按钮事件
    handleAddUser() {
      this.getUsersList();
      this.innerVisible = true;
    },
    // 前往创建新用户
    goCreateUser() {
      this.$router.push("/permission/users");
    },

    // 取消关联用户请求
    async projectDeleteUser() {
      let params = {
        projectId: this.projectUserData.projectId,
        userId: this.userIds.join(),
      };
      let { code, msg } = await projectDeleteUser(params);
      if (code === 200) {
        this.$message.success(this.$t("permissionProject.cancelled"));
        this.userIds = [];
        this.getProjectUser(params.projectId);
      }
    },
    // 单独取消关联用户
    removeUser(userId) {
      this.userIds = [];
      this.userIds.push(userId);
      this.projectDeleteUser();
    },
    // 批量取消关联选中的用户
    removeSelectUser() {
      // 通过项目id和选择的用户list移除关联项目
      this.projectDeleteUser();
    },
  },
};
</script>

<style lang="scss" scoped>
.search {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  .el-input {
    width: 300px;
    margin-right: 15px;
  }
}
</style>
