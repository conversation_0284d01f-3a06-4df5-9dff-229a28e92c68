<template>
  <div class="app-container">
    <div class="pro-detail">
      <el-page-header
        content="检查结果详情`"
        title="返回报告"
        @back="$router.go(-1)"
      />
      <el-divider />

      <div>
        <h4>{{ "检查结果" }}</h4>
        <el-table :data="tableData" border style="width: 100%">
          <el-table-column prop="trigger_time" label="触发时间" />
          <el-table-column prop="rule_name" label="触发规则" />
          <el-table-column prop="trigger_method" label="触发形式" />
          <el-table-column prop="branch" label="触发分支" />
          <el-table-column prop="result" label="检查结果" />
          <el-table-column label="错误内容" show-overflow-tooltip>
            <template slot-scope="scope">
              <span v-if="scope.row.exception_result">{{
                scope.row.exception_result + "\n"
              }}</span>
              <span v-if="Object.keys(scope.row.error_report).length !== 0">{{
                JSON.stringify(scope.row.error_report)
              }}</span>
            </template>
          </el-table-column>
        </el-table>
        <div style="display: flex; justify-content: end">
          <Pagination
            style="margin-top: 10px; padding: 10px 20px"
            :total="total"
            :page.sync="listQuery.page"
            :limit.sync="listQuery.limit"
            :auto-scroll="listQuery.limit >= 30"
            @pagination="pageSwitch"
          />
        </div>
      </div>
    </div>
    <!-- 返回顶部组件 -->
    <el-tooltip placement="top" :content="$t('backToTop')">
      <back-to-top
        :visibility-height="300"
        :back-position="50"
        transition-name="fade"
      />
    </el-tooltip>
  </div>
</template>

<script>
import BackToTop from "@/components/BackToTop";
import {
  getTableBranchDetail,
  fullScanExcel,
  getTableCheckResult,
} from "@/api/project";
import Pagination from "@/components/Pagination/index.vue";
import { mapGetters } from "vuex";

export default {
  name: "Prodetail",
  components: { Pagination, BackToTop },
  data() {
    return {
      tableData: [],
      total: 0,
      listQuery: {
        keyword: "",
        page: 1,
        limit: 20,
      },
    };
  },

  mounted() {
    this.geCheckResultList();
  },
  methods: {
    // 获取检查结果列表
    async geCheckResultList() {
      // mock data
      const data = [
        {
          trigger_time: "2024-07-03 12:00:00",
          rule_name: "规则1",
          trigger_method: "类型1",
          branch: "分支1",
          result: "通过",
          exception_result:
            "错误内容中只显示信息的部分内容后续使用省略号代替，点击查看更多时再弹出二级界面显示所有的详细的错误信息",
          error_report: {},
        },
        {
          trigger_time: "2024-07-03 12:00:00",
          rule_name: "规则2",
          trigger_method: "类型2",
          branch: "分支2",
          result: "未通过",
          exception_result: "错误2",
          error_report: {},
        },
      ];
      // this.tableData = data;
      // this.total = data.length;
      // const params = {
      //   project_id: this.$route.query.projectId,
      //   branch: this.$route.query.branch,
      //   search: this.listQuery.keyword,
      //   pageNum: this.listQuery.page,
      //   pageSize: this.listQuery.limit,
      // };
      // const { code, msg, data, total_count } = await getTableCheckResult(
      //   params
      // );
      // if (code !== 200) return this.$message.warning("检查结果" + msg);
      this.tableData = data;
      // this.total = total_count ? total_count : 0;
    },

    // 分页器页码切换 获取页码和页数
    pageSwitch(val) {
      this.geCheckResultList();
    },
    searchResult() {
      this.listQuery.page = 1;
      this.listQuery.limit = 20;
      this.geCheckResultList();
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  .pro-detail {
    background-color: #fff;
    padding: 15px;
    padding-top: 25px;
    :deep(.el-table) {
      flex: 1;
    }
  }
}
</style>
