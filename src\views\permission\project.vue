<template>
  <div class="app-container">
    <div class="app-content">
      <div class="el-page-header">
        <div class="el-page-header__content">
          {{ $t("permissionProject.projectManagement") }}
        </div>
      </div>
      <el-divider />
      <el-button type="primary" icon="el-icon-plus" @click="handleAddProject">{{
        $t("permissionProject.addProject")
      }}</el-button>
      <el-table
        :data="projectList"
        style="width: 100%; margin-top: 30px"
        :header-cell-style="{
          background: '#F7FBFF',
          height: '52px',
        }"
      >
        <el-table-column
          align="center"
          :label="$t('permissionProject.projectLogo')"
          width="110"
        >
          <el-image
            slot-scope="scope"
            style="width: 50px; height: 50px"
            :src="`${baseUrl}/media/sculpture/${scope.row.projectId}.png?time=${currentTime}`"
        /></el-table-column>
        <el-table-column
          align="center"
          :label="$t('permissionProject.projectId')"
          min-width="100"
          ><template slot-scope="scope">
            {{ scope.row.projectId }}
          </template></el-table-column
        >
        <el-table-column
          align="center"
          :label="$t('permissionProject.projectName')"
          min-width="180"
        >
          <template slot-scope="scope">
            {{ scope.row.projectName }}
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          :label="$t('permissionProject.projectDescription')"
          min-width="200"
        >
          <template slot-scope="scope">
            <!-- <el-tooltip :content="scope.row.description" placement="top">
            </el-tooltip> -->
            <span class="project-description">{{ scope.row.description }}</span>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          :label="$t('permissionProject.ignorePath')"
          min-width="200"
        >
          <template slot-scope="scope">
            <el-tag
              v-for="path in scope.row.ignorePath.slice(0, 1)"
              :key="path"
              size="small"
              type=""
              >{{ path }}</el-tag
            >
            <el-tooltip
              v-show="scope.row.ignorePath.length > 1"
              placement="top"
            >
              <div slot="content">
                <div v-for="path in scope.row.ignorePath" :key="path">
                  <el-tag size="small" type="" style="margin-bottom: 5px">{{
                    path
                  }}</el-tag>
                </div>
              </div>
              <el-button
                v-show="scope.row.ignorePath"
                icon="el-icon-more"
                type="text"
              />
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          :label="$t('permissionProject.createTime')"
          min-width="180"
        >
          <template slot-scope="scope">
            {{ scope.row.createTime }}
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          :label="$t('permissionProject.editTime')"
          min-width="200"
        >
          <template slot-scope="scope">
            {{ scope.row.editTime }}
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          :label="$t('permissionProject.operator')"
          fixed="right"
          width="225"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              icon="el-icon-setting"
              :disabled="scope.row.disabled"
              @click="setProjectUser(scope.row)"
              >{{ $t("permissionProject.setUser") }}</el-button
            >
            <el-button
              type="text"
              icon="el-icon-edit-outline"
              @click="handleEdit(scope.row)"
              >{{ $t("permissionProject.edit") }}</el-button
            >
            <el-button
              type="text"
              style="color: #f56c6c"
              icon="el-icon-delete"
              :disabled="scope.row.disabled"
              @click="handleDelete(scope.row.projectId)"
              >{{ $t("permissionProject.delete") }}</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页器 -->
      <pagination
        style="margin-top: 0; padding: 10px 20px"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="pageSwitch"
      />
    </div>
    <!-- 项目所属用户配置弹窗 -->
    <set-project-user
      :set-user-modal="setUserModal"
      :project-user-data="projectUserData"
      :closeDialog="closeProjectSetDialog"
    />
    <!-- 新增和编辑项目弹窗 -->
    <el-dialog
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      :title="
        dialogType === 'edit'
          ? $t('permissionProject.editProject')
          : $t('permissionProject.addProject')
      "
    >
      <el-form
        ref="project"
        :model="project"
        :rules="rules"
        label-width="108px"
        label-position="right"
      >
        <el-form-item :label="$t('permissionProject.projectLogo')">
          <el-upload
            class="avatar-uploader"
            action=""
            accept=".jpg,.jpeg,.png,.gif,.webp"
            :show-file-list="false"
            :auto-upload="false"
            :on-change="fileChang"
          >
            <template v-if="imageUrl">
              <img :src="imageUrl" class="avatar" />
              <div class="upload-handle" @click.stop>
                <div class="handle-icon" @click="editImg">
                  <i
                    class="el-icon-edit-outline"
                    style="padding-bottom: 4px"
                  ></i>
                  <span>{{ $t("uploadImage.change") }}</span>
                </div>
                <div class="handle-icon" @click="imgViewVisible = true">
                  <i class="el-icon-zoom-in" style="padding-bottom: 4px"></i>
                  <span>{{ $t("uploadImage.check") }}</span>
                </div>
              </div>
            </template>
            <i v-else class="el-icon-plus avatar-uploader-icon" />
          </el-upload>
        </el-form-item>
        <el-form-item
          :label="$t('permissionProject.projectName')"
          prop="projectName"
        >
          <el-input
            maxlength="30"
            v-model="project.projectName"
            :placeholder="$t('permissionProject.enterProjectName')"
          />
        </el-form-item>
        <el-form-item :label="$t('permissionProject.ignorePath')">
          <el-tag
            v-for="path in project.ignorePath"
            :key="path"
            closable
            :disable-transitions="false"
            type=""
            @close="removePath(path)"
          >
            {{ path }}
          </el-tag>
          <el-input
            v-if="inputVisible"
            ref="saveTagInput"
            v-model="ignorePath"
            class="input-new-tag"
            size="small"
            maxlength="20"
            @keyup.enter.native="handleInputConfirm"
            @blur="handleInputConfirm"
          />
          <el-button
            v-else
            class="button-new-tag"
            size="mini"
            icon="el-icon-plus"
            @click="showInput"
            >{{ $t("permissionProject.add") }}</el-button
          >
        </el-form-item>
        <el-form-item :label="$t('permissionProject.projectDescription')">
          <el-input
            v-model="project.description"
            :autosize="{ minRows: 2, maxRows: 4 }"
            type="textarea"
            maxlength="100"
            show-word-limit
            :placeholder="$t('permissionProject.enterProjectDescription')"
          />
        </el-form-item>
      </el-form>
      <div style="text-align: right">
        <el-button type="danger" @click="closeProjectDialog">{{
          $t("permissionProject.cancel")
        }}</el-button>
        <el-button type="primary" @click="confirmProject">{{
          $t("permissionProject.ok")
        }}</el-button>
      </div>
    </el-dialog>
    <!-- 查看项目Icon -->
    <el-image-viewer
      style="z-index: 2006"
      v-if="imgViewVisible"
      :on-close="closeImgViewer"
      :url-list="[imageUrl]"
    />
  </div>
</template>

<script>
import { deepClone } from "@/utils";
import SetProjectUser from "./components/SetProjectUser.vue";
import Pagination from "@/components/Pagination/index.vue";
import {
  getProjectList,
  createProject,
  editProject,
  deleteProject,
} from "@/api/permission-project";
import { uploadSculpture } from "@/api/upload";

const defaultProject = {
  projectId: null,
  projectName: "",
  description: "",
  ignorePath: [],
  createTime: "",
  editTime: "",
};
export default {
  components: {
    SetProjectUser,
    Pagination,
    "el-image-viewer": () =>
      import("element-ui/packages/image/src/image-viewer"),
  },
  data() {
    return {
      project: Object.assign({}, defaultProject),
      projectList: [], // 所有项目
      dialogVisible: false,
      dialogType: "new",
      baseUrl: process.env.VUE_APP_BASE_API, //图片基础路径
      imageUrl: "", // 项目Icon
      imgViewVisible: false, //查看Icon
      // 资源忽略设置相关
      dialogIgnoreResource: false,
      ignoreResourceProId: null,
      ignoreResourceList: [],
      inputVisible: false,
      ignorePath: "",

      total: 0,
      listQuery: {
        page: 1,
        limit: 10,
      },
      uploadFile: "",
      currentTime: Date.now(),

      rules: {
        projectName: [
          {
            required: true,
            message: this.$t("permissionProject.enterProjectName"),
            trigger: "blur",
          },
          // {
          //   min: 3,
          //   max: 15,
          //   message: this.$t("permissionProject.characterLength"),
          //   trigger: "blur",
          // },
        ],
      },

      // 配置项目所属用户相关
      projectUserData: [],
      userIds: [],
      setUserModal: false,
    };
  },

  created() {
    // 获取项目列表
    this.getProjects();
    // 获取用户列表
  },

  methods: {
    // 查看图片
    closeImgViewer() {
      this.imgViewVisible = false;
    },
    // 更换图片
    editImg() {
      const dom = document.querySelector(`.el-upload__input`);
      dom && dom.dispatchEvent(new MouseEvent("click"));
    },

    // 获取所有项目
    async getProjects() {
      let params = {
        pageNum: this.listQuery.page,
        pageSize: this.listQuery.limit,
      };
      let { code, projectList, dataLen } = await getProjectList(params);
      if (code === 200) {
        projectList.forEach((item) => {
          if (item.ignorePath) {
            item.ignorePath = item.ignorePath.split(",");
          } else {
            item.ignorePath = [];
          }
        });
        projectList.ignorePath = [];
        this.projectList = projectList;
        this.total = dataLen;
      }
    },
    // 分页器页码改变获取项目数据
    pageSwitch(value) {
      this.getProjects();
    },

    // 新增项目
    handleAddProject() {
      this.project = Object.assign({}, defaultProject);
      this.project.ignorePath = [];
      this.dialogType = "new";
      this.imageUrl = ""; // 上传图片默认为空
      this.dialogVisible = true;
    },
    // 编辑项目
    handleEdit(row) {
      this.dialogType = "edit";
      this.dialogVisible = true;
      this.project = deepClone(row);
      this.imageUrl = `${this.baseUrl}/media/sculpture/${
        row.projectId
      }.png?time=${Date.now()}`; // 保存到上传默认图片
    },
    // 删除项目
    handleDelete(projectId) {
      this.$confirm(
        this.$t("permissionProject.isDeleteProject"),
        this.$t("permissionProject.tips"),
        {
          type: "warning",
        }
      )
        .then(async () => {
          let { code, msg } = await deleteProject({ projectId });
          if (code === 200) {
            this.$message({
              type: "success",
              message: this.$t("permissionProject.deleteSuccessful"),
            });
            this.getProjects();
            this.currentTime = Date.now();
          }
        })
        .catch((err) => {
          console.error(err);
        });
    },

    // 项目编辑/新增关闭按钮
    closeProjectDialog() {
      this.$refs.project.resetFields();
      this.dialogVisible = false;
    },
    // 项目编辑/新增确认按钮
    async confirmProject() {
      const isEdit = this.dialogType === "edit";
      this.$refs.project.validate(async (valid) => {
        if (valid) {
          if (isEdit) {
            let params = {
              projectId: this.project.projectId,
              projectName: this.project.projectName,
              ignorePath: this.project.ignorePath.toString(),
              description: this.project.description,
            };
            let { code, msg } = await editProject(params);
            if (code === 200) {
              this.uploadIcon(this.uploadFile, params.projectId);
              this.$message({
                type: "success",
                message: this.$t("permissionProject.editSuccessful"),
              });
              this.getProjects();
              this.closeProjectDialog();
            } else {
              this.$message({
                type: "error",
                message: msg,
              });
            }
          } else {
            let params = {
              projectName: this.project.projectName,
              ignorePath: this.project.ignorePath.toString(),
              description: this.project.description,
            };
            const { code, msg, projectId } = await createProject(params);
            if (code === 200) {
              this.uploadIcon(this.uploadFile, projectId);
              this.$message({
                type: "success",
                message: this.$t("permissionProject.addSuccessful"),
              });
              this.getProjects();
              this.closeProjectDialog();
            } else {
              this.$message({
                type: "error",
                message: msg,
              });
            }
          }
        } else {
          this.$message.warning(this.$t("permissionProject.RequiredTips"));
        }
      });
    },
    // 上传项目icon
    async uploadIcon(uploadFile, projectId) {
      if (!uploadFile) return;
      const form = new FormData(); // FormData 对象
      form.append("photo", uploadFile);
      form.append("projectId", projectId);
      let { code, msg } = await uploadSculpture(form);
      if (code !== 200) this.$message.error(msg);
      this.currentTime = Date.now();
      //上传完重置值
      this.uploadFile = undefined;
    },
    // 获取上传项目icon的url
    fileChang(file, fileList) {
      // 上传项目icon前检查格式
      const isJPG =
        file.raw.type === "image/jpeg" ||
        file.raw.type === "image/png" ||
        file.raw.type === "image/webp" ||
        file.raw.type === "image/gif";
      const isLt2M = file.raw.size / 1024 / 1024 < 2;
      if (!isJPG) {
        this.$message.error(this.$t("permissionProject.isJPG"));
      }
      if (!isLt2M) {
        this.$message.error(this.$t("permissionProject.isLt2M"));
      }
      if (!isJPG || !isLt2M) return;
      this.imageUrl = URL.createObjectURL(file.raw);
      this.uploadFile = file.raw;
    },

    // 设置资源忽略路径
    setIgnoreResource(row) {
      this.ignoreResourceProId = row.id;
      this.ignoreResourceList = row.ignoreResourceList;
      this.dialogIgnoreResource = true;
    },
    removePath(path) {
      this.project.ignorePath.splice(this.project.ignorePath.indexOf(path), 1);
    },
    showInput() {
      this.inputVisible = true;
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    handleInputConfirm() {
      const inputValue = this.ignorePath;
      if (inputValue) {
        for (const path of this.project.ignorePath) {
          if (path === inputValue) {
            this.$message({
              type: "warning",
              message: this.$t("permissionProject.existPath"),
            });
            // this.inputVisible = false;
            this.ignorePath = "";
            this.$nextTick((_) => {
              this.$refs.saveTagInput.$refs.input.focus();
            });
            return;
          }
        }
      }
      if (this.ignorePath !== "") {
        this.project.ignorePath.push(this.ignorePath);
        this.inputVisible = false;
        this.ignorePath = "";
      } else {
        this.inputVisible = false;
        this.ignorePath = "";
      }
    },
    confirmIgnoreResource() {
      for (let index = 0; index < this.projectList.length; index++) {
        if (this.projectList[index].id === this.ignoreResourceProId) {
          this.projectList.ignoreResourceList = this.ignoreResourceList;
          break;
        }
      }
      this.dialogIgnoreResource = false;
    },
    // 关闭配属用户弹窗
    closeProjectSetDialog() {
      this.setUserModal = false;
    },
    // 设置项目所属用户
    async setProjectUser(row) {
      this.setUserModal = true;
      this.projectUserData = row;
    },
    getSelectUser(value) {
      this.userIds = value;
    },
    confirmUserProject() {
      // 后续上传数据
      // 遍历用户id 针对id推送项目数据
      this.setUserModal = false;
    },

    // 上传头像成功
    handleAvatarSuccess(res, file) {
      this.imageUrl = URL.createObjectURL(file.raw);
    },
  },
};
</script>

<style lang="scss" scoped>
// 修改elementui表格的默认样式
::v-deep .el-table__body-wrapper {
  // cursor: pointer;

  &::-webkit-scrollbar {
    height: 6px;
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    // 轨道颜色
    background-color: #fff;
  }

  &::-webkit-scrollbar-thumb {
    // 滚动块颜色
    background-color: #e6e9ed;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    // hover高亮
    background-color: #d5d8db;
  }
}
// 当表格没有滚动条时
::v-deep .el-table__body-wrapper.is-scrolling-none ~ .el-table__fixed-right {
  height: 100% !important;
  bottom: 0 !important;
  box-shadow: none !important;
}

// 当表格有纵向滚动条时
::v-deep .el-table--scrollable-y .el-table__fixed-right {
  right: 6px !important;
}

// 当表格只有横向滚动条，没有纵向滚动条时
::v-deep
  .el-table--scrollable-x:not(.el-table--scrollable-y)
  .el-table__fixed-right {
  right: 0 !important;
}
// 处理有x滚动条时高度遮挡滚动条
::v-deep .el-table--scrollable-x {
  .el-table__fixed,
  .el-table__fixed-right {
    height: calc(100% - 6px) !important;
  }
}
// 存在滚动条的时候在right
::v-deep .el-table--scrollable-y {
  .el-table__fixed-right {
    right: 6px !important;
  }
}
// 解决修改滚动条底部不对齐问题
::v-deep .el-scrollbar__wrap::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.app-container {
  background-color: #f6f8f9;
}
.avatar-uploader ::v-deep.el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader ::v-deep.el-upload:hover {
  border-color: #409eff;
  .upload-handle {
    opacity: 1;
  }
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}
.avatar {
  width: 100px;
  height: 100px;
  display: block;
}
.upload-handle {
  position: absolute;
  top: 0;
  right: 0;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  cursor: pointer;
  background: rgb(0 0 0 / 60%);
  opacity: 0;
  transition: var(--el-transition-duration-fast);
  .handle-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0 6%;
    color: aliceblue;
    .el-icon {
      margin-bottom: 40%;
      font-size: 130%;
      line-height: 130%;
    }
    span {
      font-size: 85%;
      line-height: 85%;
    }
  }
}

.project-description {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  /*autoprefixer: ignore next */
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  word-break: break-all;
}

::v-deep.el-tag + .el-tag {
  margin-left: 10px;
}
::v-deep.el-tag--medium {
  height: 32px;
  line-height: 32px;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>
