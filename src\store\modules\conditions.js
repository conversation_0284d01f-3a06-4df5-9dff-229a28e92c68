const state = {
  conditionKey: null,
  conditionGroupKey: null,
  ruleConfig: [], //规则设置参数
  stypeIndex: 0,
  operatorType: -1,

  // 保存所有规则参数的数据
  form: {
    stype: "",
    ruleLevel: "middle",
    isEnabled: true,
    name: "",
    conditions: [],
    asserts: [
      // {
      //   key: Date.now(), //绑定的key值,通过key值方便找到对应条件组以执行相关的操作
      //   type: "group",
      //   prefix: "true",
      //   operator: "and",
      //   value: [],
      // },
    ],
  },
};

const mutations = {
  setFormData(state, formData) {
    state.form = Object.assign(state.form, formData);
  },
  // 获取上传的文件数据或者规则列表复用的数据替换所有表单数据
  setUploadData(state, uploadData) {
    state.form.stype = uploadData.stype;
    state.form.ruleLevel = uploadData.ruleLevel;
    state.form.name = uploadData.name;
    state.form.isEnabled = uploadData.isEnabled;
    if (!uploadData.assert.type) {
      // console.log('空的');
      state.form.asserts = [];
    } else {
      state.form.asserts = [];
      state.form.asserts.push(uploadData.assert);
    }
    if (!uploadData.condition.type) {
      // console.log('空的');
      state.form.conditions = [];
    } else {
      state.form.conditions = [];
      state.form.conditions.push(uploadData.condition);
    }
  },

  // 根据对应条件的key判断条件的操作符的类型 改变value值数据类型
  CHANGE_META_VALUE(state, data) {
    for (const item of data) {
      if (item.key === state.conditionKey) {
        // 修改后：根据操作符类型设置不同的初始值
        if (item.operator === "range") {
          item.value = ["", ""]; // range 操作符需要数组
        } else if (item.operator === "in") {
          item.value = ""; // in 操作符需要字符串（逗号分隔）
        } else {
          item.value = "";
        }
        
        // 修改前的逻辑（注释保留）
        // if (item.operator === "in") {
        //   item.value = ["", ""];
        // } else {
        //   item.value = "";
        // }
        return;
      }

      // 如果item.value存在并且是数组，就再调用一次函数，传数组给他，直到没有value数组结束
      if (item.value && item.value.length > 0) {
        this.commit("conditions/CHANGE_META_VALUE", item.value);
      }
    }
  },
  // 点击切换规则操作符类型
  CHANGE_OPERATOR_TYPE(state, data) {
    for (const item of data) {
      if (item.key === state.conditionKey) {
        // console.log(item);
        item.operatorType = state.operatorType;
        item.operator = "";
        // 根据操作符类型设置不同的初始值
        if (state.operatorType === 0) {
          item.value = ""; // 数值类型，默认为字符串，根据具体操作符调整
        } else if (state.operatorType === 1) {
          item.value = ""; // 字符串类型
        } else if (state.operatorType === 2) {
          item.value = true; // 布尔类型
        } else {
          item.value = "";
        }
        return;
      }

      // 如果item.value存在并且是数组，就再调用一次函数，传数组给他，直到没有value数组结束
      if (item.value && item.value.length > 0) {
        this.commit("conditions/CHANGE_OPERATOR_TYPE", item.value);
      }
    }
  },
  SET_OPERATOR_TYPE(state, type) {
    state.operatorType = type;
  },
  // 保存规则参数
  setRuleConfig(state, data) {
    state.ruleConfig = data;
  },

  setStypeIndex(state, index) {
    if (index === state.stypeIndex) return;
    state.stypeIndex = index === -1 ? 0 : index; //把获取的类型索引值保存
    state.stype = index === -1 ? "" : state.ruleConfig[state.stypeIndex].type;
    // 点击切换类型后清空数据
    state.form.conditions = [];
    state.form.asserts = [];
  },
  SET_KEY: (state, key) => {
    state.conditionKey = key;
  },
  SET_GROUP_KEY: (state, key) => {
    state.conditionGroupKey = key;
  },
  // 添加条件
  ADD_CONDITION(state, data) {
    for (const item of data) {
      // 如果是条件后边的添加按钮 则循环遍历对应key所在的条件,然后执行在下方添加新的条件
      if (item.type === "meta" && item.key === state.conditionKey) {
        const index = data.indexOf(item);
        if (index !== -1) {
          data.splice(index + 1, 0, {
            key: Date.now(),
            stypeIndex: state.stypeIndex,
            operatorType: -1,
            type: "meta",
            prefix: "true",
            field: "",
            operator: "",
            value: "", // 初始化为空字符串，根据操作符动态调整
          });
        }
        return;
      }

      // 如果是添加条件按钮 找到对应组所在的key 直接push条件
      if (
        item.type === "group" &&
        item.key === state.conditionKey &&
        item.value.length >= 0
      ) {
        item.value.push({
          key: Date.now(),
          stypeIndex: state.stypeIndex,
          operatorType: -1,
          type: "meta",
          prefix: "true",
          field: "",
          operator: "",
          value: "", // 初始化为空字符串，根据操作符动态调整
        });
        return;
      }
      // 如果item.value存在并且是数组，就再调用一次函数，传数组给他，直到没有value数组结束
      if (item.value && item.value.length > 0) {
        this.commit("conditions/ADD_CONDITION", item.value);
      }
    }
  },
  // 删除对应key的条件
  REMOVE_CONDITION(state, data) {
    // 循环遍历找到对应条件的key所在 进行删除此对应key的数据
    for (const item of data) {
      if (item.key === state.conditionKey) {
        // console.log("找到了item.key", item.key);
        const index = data.indexOf(item);
        if (index !== -1) {
          data.splice(index, 1);
        }
        return;
      }
      // 如果item.value存在并且是数组，就再调用一次函数，传数组给他，直到没有value数组结束
      if (item.value && item.value.length > 0) {
        this.commit("conditions/REMOVE_CONDITION", item.value);
      }
    }
  },

  // 添加条件组
  ADD_CONDITION_GROUP(state, data) {
    for (const item of data) {
      // 如果是条件后边添加按钮 则遍历数据数组匹配条件组的key,执行添加条件组数据
      if (item.type === "meta" && item.key === state.conditionGroupKey) {
        // console.log("找到了条件组key", item.key);
        const index = data.indexOf(item);
        if (index !== -1) {
          data.splice(index + 1, 0, {
            key: Date.now(),
            type: "group",
            prefix: "true",
            operator: "and",
            value: [
              {
                key: Date.now() - 10,
                stypeIndex: state.stypeIndex,
                operatorType: -1,
                type: "meta",
                prefix: "true",
                field: "",
                operator: "",
                value: "", // 初始化为空字符串，根据操作符动态调整
              },
            ],
          });
        }
        return;
      }
      // 如果是添加条件组按钮 找到对应所在组的key push条件组数据
      if (
        item.type === "group" &&
        item.key === state.conditionGroupKey &&
        item.value.length >= 0
      ) {
        // console.log(item);
        item.value.push({
          key: Date.now(),
          type: "group",
          prefix: "true",
          operator: "and",
          value: [
            {
              key: Date.now() - 10,
              stypeIndex: state.stypeIndex,
              operatorType: -1,
              type: "meta",
              prefix: "true",
              field: "",
              operator: "",
              value: "", // 初始化为空字符串，根据操作符动态调整
            },
          ],
        });
        return;
      }

      if (item.value && item.value.length > 0) {
        this.commit("conditions/ADD_CONDITION_GROUP", item.value);
      }
    }
  },

  // 删除对应key的条件组
  REMOVE_CONDITION_GROUP(state, data) {
    for (const item of data) {
      if (item.key === state.conditionGroupKey) {
        // console.log("找到了item.key", item.key);
        const index = data.indexOf(item);
        if (index !== -1) {
          data.splice(index, 1);
        }
        return;
      }

      if (item.value && item.value.length > 0) {
        this.commit("conditions/REMOVE_CONDITION_GROUP", item.value);
      }
    }
  },

  // 当没有条件组数据时先创建第一个条件组
  addFirstConditionGroup(state, createType) {
    if (createType === "condition") {
      state.form.conditions.push({
        key: Date.now(),
        type: "group",
        prefix: "true",
        operator: "and",
        value: [],
      });
      return;
    }

    if (createType === "assert") {
      state.form.asserts.push({
        key: Date.now(),
        type: "group",
        prefix: "true",
        operator: "and",
        value: [],
      });
    }
  },
};

const actions = {
  getOperatorValue({ commit, state }, key) {
    commit("SET_KEY", key);

    commit("CHANGE_META_VALUE", state.form.conditions);
    if (state.form.asserts && state.form.asserts.length > 0) {
      commit("CHANGE_META_VALUE", state.form.asserts);
    }
  },
  getOperatorType({ commit, state }, data) {
    let { type, key } = data;
    commit("SET_KEY", key);
    commit("SET_OPERATOR_TYPE", type);

    commit("CHANGE_OPERATOR_TYPE", state.form.conditions);
    if (state.form.asserts && state.form.asserts.length > 0) {
      commit("CHANGE_OPERATOR_TYPE", state.form.asserts);
    }
  },

  addCondition({ commit, state }, key) {
    commit("SET_KEY", key);
    commit("ADD_CONDITION", state.form.conditions);
    if (state.form.asserts && state.form.asserts.length > 0) {
      commit("ADD_CONDITION", state.form.asserts);
    }
  },

  removeCondition({ commit, state }, key) {
    commit("SET_KEY", key);
    commit("REMOVE_CONDITION", state.form.conditions);
    if (state.form.asserts && state.form.asserts.length > 0) {
      commit("REMOVE_CONDITION", state.form.asserts);
    }
  },

  addConditionGroup({ commit, state }, key) {
    commit("SET_GROUP_KEY", key);
    commit("ADD_CONDITION_GROUP", state.form.conditions);
    if (state.form.asserts && state.form.asserts.length > 0) {
      commit("ADD_CONDITION_GROUP", state.form.asserts);
    }
  },

  removeConditionGroup({ commit, state }, key) {
    commit("SET_GROUP_KEY", key);
    commit("REMOVE_CONDITION_GROUP", state.form.conditions);
    if (state.form.asserts && state.form.asserts.length > 0) {
      commit("REMOVE_CONDITION_GROUP", state.form.asserts);
    }
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
