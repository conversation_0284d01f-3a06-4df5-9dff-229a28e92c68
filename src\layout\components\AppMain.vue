<template>
  <section class="app-main">
    <transition name="fade-transform" mode="out-in">
      <div style="margin-bottom: 150px">
        <router-view :key="key" />
      </div>
    </transition>
    <div class="footer-copy-right">
      <p>
        Copyright &copy; All Rights Reserved | AssetsLint 资源自动化检测平台
      </p>
    </div>
  </section>
</template>

<script>
export default {
  name: "AppMain",
  computed: {
    cachedViews() {
      return this.$store.state.tagsView.cachedViews;
    },
    key() {
      return this.$route.path;
    },
  },
};
</script>

<style lang="scss" scoped>
.footer-copy-right {
  width: 100%;
  position: absolute;
  bottom: 0;
  p {
    text-align: center;
    color: #232f55;
    font-weight: 400;
    font-size: 14px;
    line-height: 2;
    font-family: "Mulish", sans-serif;
    user-select: none;
    margin: 0;

    font-family: MicrosoftYaHei;
    font-size: 14px;
    color: #7a7a7a;
    letter-spacing: 0;
    line-height: 22px;
    font-weight: 400;
    margin-top: 75px;
    margin-bottom: 32px;
  }
}
.app-main {
  /* 50= navbar  50  */
  min-height: calc(100vh);
  width: 100%;
  position: relative;
  overflow: hidden;
  background-color: #f6f8f9;
}

.fixed-header + .app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    min-height: calc(100vh - 84px);
  }

  .fixed-header + .app-main {
    padding-top: 84px;
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
</style>