<template>
  <div class="app-container">
    <div class="report-comparison">
      <el-page-header
        :content="$t('reportComparison.reportComparison')"
        :title="$t('reportComparison.backToProjectReport')"
        @back="$router.back()"
      />
      <el-divider />
      <div>
        <CompareTable
          :tableTitle="`${$t('reportComparison.testId')}:${
            $route.query.id1 + $t('reportComparison.comparedTestId')
          }:${$route.query.id2 + $t('reportComparison.extraData')}`"
          :reportData="reportData1"
          :reportDataLen="reportData1.length"
        />
      </div>
      <el-divider />
      <div>
        <CompareTable
          :tableTitle="`${$t('reportComparison.testId')}:${
            $route.query.id1 + $t('reportComparison.comparedTestId')
          }:${$route.query.id2 + $t('reportComparison.missingData')}`"
          :reportData="reportData2"
          :reportDataLen="reportData2.length"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { getReportCompare } from "@/api/project";

import CompareTable from "./components/CompareTable.vue";
export default {
  components: { CompareTable },
  data() {
    return {
      reportData1: [],
      reportData2: [],
    };
  },
  mounted() {
    this.getReportData();
  },
  methods: {
    async getReportData() {
      let id1 = this.$route.query.id1;
      let id2 = this.$route.query.id2;
      if (!id1 && !id2) {
        this.$router.push("/project/index");
      }
      let params = {
        projectId: this.$route.query.projectId,
        branch: this.$route.query.branch,
        id1,
        id2,
      };
      let { code, extraData, lackData } = await getReportCompare(params);
      if (code === 200) {
        this.reportData1 = extraData;
        this.reportData2 = lackData;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  .report-comparison {
    background-color: #fff;
    padding: 15px;
    padding-top: 25px;
  }
}
</style>
