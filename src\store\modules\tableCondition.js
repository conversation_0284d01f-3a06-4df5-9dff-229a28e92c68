const state = {
  conditionKey: null,
  conditionGroupKey: null,
  ruleConfig: [], //规则设置参数
  operatorType: -1,

  // 保存所有规则参数的数据
  tableForm: {
    // stype: "",
    isEnabled: true,
    name: "",
    ruleLevel: "normal",
    tableHeaders: [],
    conditions: [],
    calc: [],
    mergeParams: [],
    asserts: [],
    description: "",
  },
};

const mutations = {
  setTableFormData(state, formData) {
    state.tableForm = Object.assign(state.tableForm, formData);
  },
  // 获取上传的文件数据或者规则列表复用的数据替换所有表单数据
  setUploadData(state, uploadData) {
    state.tableForm.name = uploadData.name;
    state.tableForm.ruleLevel = uploadData.ruleLevel;
    state.tableForm.isEnabled = uploadData.isEnabled;
    state.tableForm.tableHeaders = uploadData.tableHeaders
      ? uploadData.tableHeaders
      : [];
    if (!uploadData.condition.type) {
      state.tableForm.conditions = [];
    } else {
      state.tableForm.conditions = [];
      state.tableForm.conditions.push(uploadData.condition);
    }
    state.tableForm.calc = uploadData.calc ? uploadData.calc : [];
    state.tableForm.mergeParams = uploadData.mergeParams
      ? uploadData.mergeParams
      : [];
    if (!uploadData.assert.type) {
      state.tableForm.asserts = [];
    } else {
      state.tableForm.asserts = [];
      state.tableForm.asserts.push(uploadData.assert);
    }
    state.tableForm.description = uploadData.description
      ? uploadData.description
      : "";
  },

  // 根据对应条件的key判断条件的操作符的类型 改变value值数据类型
  CHANGE_META_VALUE(state, data) {
    for (const item of data) {
      if (item.key === state.conditionKey) {
        if (item.operator === "in") {
          item.value = ["", ""];
        } else {
          item.value = "";
        }
        return;
      }

      // 如果item.value存在并且是数组，就再调用一次函数，传数组给他，直到没有value数组结束
      if (item.value && item.value.length > 0) {
        this.commit("tableCondition/CHANGE_META_VALUE", item.value);
      }
    }
  },
  // 点击切换规则操作符类型
  CHANGE_OPERATOR_TYPE(state, data) {
    for (const item of data) {
      if (item.key === state.conditionKey) {
        item.operatorType = state.operatorType;
        item.operator = "";
        item.value = "";
        return;
      }

      // 如果item.value存在并且是数组，就再调用一次函数，传数组给他，直到没有value数组结束
      if (item.value && item.value.length > 0) {
        this.commit("tableCondition/CHANGE_OPERATOR_TYPE", item.value);
      }
    }
  },
  SET_OPERATOR_TYPE(state, type) {
    state.operatorType = type;
  },
  // 保存规则参数
  setRuleConfig(state, data) {
    state.ruleConfig = data;
    // 点击切换表格名后清空数据
    state.tableForm.conditions = [];
    state.tableForm.asserts = [];
  },

  SET_KEY: (state, key) => {
    state.conditionKey = key;
  },
  SET_GROUP_KEY: (state, key) => {
    state.conditionGroupKey = key;
  },
  // 添加条件
  ADD_CONDITION(state, data) {
    for (const item of data) {
      // 如果是条件后边的添加按钮 则循环遍历对应key所在的条件,然后执行在下方添加新的条件
      if (item.type === "meta" && item.key === state.conditionKey) {
        const index = data.indexOf(item);
        if (index !== -1) {
          data.splice(index + 1, 0, {
            key: Date.now(),
            // operatorType: -1,
            type: "meta",
            // prefix: "true",
            // field: "",
            tableHeader: "",
            operator: "",
            value: "",
          });
        }
        return;
      }

      // 如果是添加条件按钮 找到对应组所在的key 直接push条件
      if (
        item.type === "group" &&
        item.key === state.conditionKey &&
        item.value.length >= 0
      ) {
        item.value.push({
          key: Date.now(),
          // operatorType: -1,
          type: "meta",
          // prefix: "true",
          // field: "",
          tableHeader: "",
          operator: "",
          value: "",
        });
        return;
      }
      // 如果item.value存在并且是数组，就再调用一次函数，传数组给他，直到没有value数组结束
      if (item.value && item.value.length > 0) {
        this.commit("tableCondition/ADD_CONDITION", item.value);
      }
    }
  },
  // 删除对应key的条件
  REMOVE_CONDITION(state, data) {
    // 循环遍历找到对应条件的key所在 进行删除此对应key的数据
    for (const item of data) {
      if (item.key === state.conditionKey) {
        const index = data.indexOf(item);
        if (index !== -1) {
          data.splice(index, 1);
        }
        return;
      }
      // 如果item.value存在并且是数组，就再调用一次函数，传数组给他，直到没有value数组结束
      if (item.value && item.value.length > 0) {
        this.commit("tableCondition/REMOVE_CONDITION", item.value);
      }
    }
  },

  // 添加条件组
  ADD_CONDITION_GROUP(state, data) {
    for (const item of data) {
      // 如果是条件后边添加按钮 则遍历数据数组匹配条件组的key,执行添加条件组数据
      if (item.type === "meta" && item.key === state.conditionGroupKey) {
        const index = data.indexOf(item);
        if (index !== -1) {
          data.splice(index + 1, 0, {
            key: Date.now(),
            type: "group",
            // prefix: "true",
            operator: "and",
            value: [
              {
                key: Date.now() - 10,
                // operatorType: -1,
                type: "meta",
                // prefix: "true",
                // field: "",
                tableHeader: "",
                operator: "",
                value: "",
              },
            ],
          });
        }
        return;
      }
      // 如果是添加条件组按钮 找到对应所在组的key push条件组数据
      if (
        item.type === "group" &&
        item.key === state.conditionGroupKey &&
        item.value.length >= 0
      ) {
        item.value.push({
          key: Date.now(),
          type: "group",
          // prefix: "true",
          operator: "and",
          value: [
            {
              key: Date.now() - 10,
              // operatorType: -1,
              type: "meta",
              // prefix: "true",
              // field: "",
              tableHeader: "",
              operator: "",
              value: "",
            },
          ],
        });
        return;
      }

      if (item.value && item.value.length > 0) {
        this.commit("tableCondition/ADD_CONDITION_GROUP", item.value);
      }
    }
  },

  // 删除对应key的条件组
  REMOVE_CONDITION_GROUP(state, data) {
    for (const item of data) {
      if (item.key === state.conditionGroupKey) {
        const index = data.indexOf(item);
        if (index !== -1) {
          data.splice(index, 1);
        }
        return;
      }

      if (item.value && item.value.length > 0) {
        this.commit("tableCondition/REMOVE_CONDITION_GROUP", item.value);
      }
    }
  },

  // 当没有条件组数据时先创建第一个条件组
  addFirstConditionGroup(state, createType) {
    if (createType === "condition") {
      state.tableForm.conditions.push({
        key: Date.now(),
        type: "group",
        // prefix: "true",
        operator: "and",
        value: [],
      });
      return;
    }

    if (createType === "assert") {
      state.tableForm.asserts.push({
        key: Date.now(),
        type: "group",
        // prefix: "true",
        operator: "and",
        value: [],
      });
    }
  },
};

const actions = {
  getOperatorValue({ commit, state }, key) {
    commit("SET_KEY", key);

    commit("CHANGE_META_VALUE", state.tableForm.conditions);
    if (state.tableForm.asserts && state.tableForm.asserts.length > 0) {
      commit("CHANGE_META_VALUE", state.tableForm.asserts);
    }
  },
  getOperatorType({ commit, state }, data) {
    let { type, key } = data;
    commit("SET_KEY", key);
    commit("SET_OPERATOR_TYPE", type);

    commit("CHANGE_OPERATOR_TYPE", state.tableForm.conditions);
    if (state.tableForm.asserts && state.tableForm.asserts.length > 0) {
      commit("CHANGE_OPERATOR_TYPE", state.tableForm.asserts);
    }
  },

  addCondition({ commit, state }, key) {
    commit("SET_KEY", key);
    commit("ADD_CONDITION", state.tableForm.conditions);
    if (state.tableForm.asserts && state.tableForm.asserts.length > 0) {
      commit("ADD_CONDITION", state.tableForm.asserts);
    }
  },

  removeCondition({ commit, state }, key) {
    commit("SET_KEY", key);
    commit("REMOVE_CONDITION", state.tableForm.conditions);
    if (state.tableForm.asserts && state.tableForm.asserts.length > 0) {
      commit("REMOVE_CONDITION", state.tableForm.asserts);
    }
  },

  addConditionGroup({ commit, state }, key) {
    commit("SET_GROUP_KEY", key);
    commit("ADD_CONDITION_GROUP", state.tableForm.conditions);
    if (state.tableForm.asserts && state.tableForm.asserts.length > 0) {
      commit("ADD_CONDITION_GROUP", state.tableForm.asserts);
    }
  },

  removeConditionGroup({ commit, state }, key) {
    commit("SET_GROUP_KEY", key);
    commit("REMOVE_CONDITION_GROUP", state.tableForm.conditions);
    if (state.tableForm.asserts && state.tableForm.asserts.length > 0) {
      commit("REMOVE_CONDITION_GROUP", state.tableForm.asserts);
    }
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
