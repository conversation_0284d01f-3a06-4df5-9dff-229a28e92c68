<template>
  <div class="app-container">
    <div class="md-body">
      <markdown02 />
      <markdown02 />
      <markdown02 />
      <markdown02 />
      <el-divider></el-divider>
      <markdown03 />
      <el-divider></el-divider>
      <div class="page-nav">
        <p class="inner">
          <span class="prev">
            <i class="el-icon-back"></i>
            <router-link to="/helpguide/guide1"> 帮助文档01 </router-link></span
          >
          <span class="next"
            ><router-link to="/helpguide/guide1"> 帮助文档03 </router-link>
            <i class="el-icon-right"></i>
          </span>
        </p>
      </div>
    </div>
  </div>
</template>

<script>
// 引入 markdown 文件，引入后是一个组件，需要在 components 中注册
import markdown02 from "./components/markdown02.md";
import markdown03 from "./components/markdown03.md";
export default {
  components: { markdown02, markdown03 },
};
</script>

<style lang="scss" scoped>
.app-container {
  width: 100%;
  display: flex;
  justify-content: center;
}
.md-body {
  width: 820px;
  padding: 1.2rem;
  background-color: #fff;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Arial, sans-serif;
}

@media (max-width: 820px) {
  .md-body {
    width: 100%;
  }
}

.page-nav .inner {
  display: flex;
  justify-content: space-between;
  a {
    font-size: 18px;
    color: #3eaf7c;
    font-weight: 500;
  }
}
</style>