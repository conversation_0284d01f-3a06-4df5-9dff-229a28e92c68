import request from '@/utils/request'
import Qs from 'qs'

// const routesMap = [
//   {
//     path: '/',
//     children: [
//       {
//         path: 'home',
//         meta: { title: '首页' }
//       }
//     ]
//   },
//   {
//     path: '/project',
//     children: [
//       {
//         path: 'index',
//         meta: { title: '我的项目' }
//       }
//     ]
//   },
//   {
//     path: '/prodetail',
//     meta: { title: '项目详情' },
//     children: [
//       {
//         path: 'report',
//         meta: { title: '项目报告' }
//       },
//       {
//         path: 'reportdetail',
//         meta: { title: '报告详情' }
//       },
//       {
//         path: 'reportcomparison',
//         meta: { title: '报告对比' }
//       }
//     ]
//   },
//   // {
//   //   path: '/proreport',
//   //   children: [
//   //     {
//   //       path: 'detail',
//   //       meta: { title: '报告详情' }
//   //     },
//   //     // {
//   //     //   path: 'comparison',
//   //     //   meta: { title: '报告对比' }
//   //     // }
//   //   ]
//   // },
//   // {
//   //   path: '/proreport',
//   //   children: [
//   //     {
//   //       path: 'comparison',
//   //       meta: { title: '报告对比' }
//   //     }
//   //   ]
//   // },

//   {
//     path: '/rulelist',
//     children: [
//       {
//         path: 'index',
//         meta: { title: '规则列表' }
//       }
//     ]
//   },
//   {
//     path: '/ruleedit',
//     children: [
//       {
//         path: 'checkrule',
//         meta: { title: '规则编辑' }
//       }
//     ]
//   },

//   {
//     path: '/rulecreate',
//     children: [
//       {
//         path: 'index',
//         meta: { title: '创建规则' }
//       }
//     ]
//   },
//   {
//     path: '/permission',
//     meta: {
//       title: '权限配置'
//     },
//     children: [
//       {
//         path: 'role',
//         meta: {
//           title: '角色管理'
//         }
//       },
//       {
//         path: 'project',
//         meta: {
//           title: '项目管理'
//         }
//       },
//       {
//         path: 'users',
//         meta: {
//           title: '用户管理'
//         }
//       }
//     ]
//   },
//   {
//     path: '/helpguide',
//     meta: {
//       title: '帮助指引'
//     },
//     children: [
//       {
//         path: 'guide1',
//         meta: {
//           title: '帮助指引01'
//         }
//       },
//       {
//         path: 'guide2',
//         meta: {
//           title: '帮助指引02'
//         }
//       }
//     ]
//   }
// ]

export function login(data) {
  data = Qs.stringify(data)
  return request({
    url: '/sign_in',
    method: 'post',
    data
  })
}

// export function getInfo(token) {
//   return request({
//     // url: '/vue-element-admin/user/info',
//     url: 'users/userinfo',
//     method: 'get',
//     params: { token }
//   })
// }
export function getInfo() {
  return request({
    url: '/get_info',
    method: 'get'
  })
  // userInfo.data.routesMap = routesMap
  // return userInfo
}

export function logout() {
  return request({
    url: '/sign_out',
    method: 'post'
  })
}

export function updateUserInfo(data) {
  data = Qs.stringify(data)
  return request({
    url: '/up_user_info',
    method: 'post',
    data
  })
}

export function updatePwd(data) {
  data = Qs.stringify(data)
  return request({
    url: '/up_password',
    method: 'post',
    data
  })
}