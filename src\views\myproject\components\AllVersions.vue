<template>
  <div>
    <div class="el-page-header">
      <div class="el-page-header__content">
        {{ projectData.projectName }}{{ $t("project.allVersions") }}
      </div>
      <!-- 分支报告上传 -->
      <upload-report :projectData="projectData"></upload-report>
    </div>
    <el-divider />

    <el-row :gutter="20">
      <el-col v-for="(item, i) in branchsData" :key="i" :span="spanNum">
        <div
          v-if="Object.keys(item).length > 0"
          class="card"
          style="margin-bottom: 15px"
          @click="goProjectDetail(item)"
        >
          <el-card shadow="hover" style="cursor: pointer; border-radius: 0">
            <div
              style="display: flex; justify-items: center; align-items: center"
            >
              <div>
                <img
                  :src="`${baseUrl}/media/sculpture/${
                    projectData.projectId
                  }.png?time=${Date.now()}`"
                  alt=""
                  style="width: 75px; height: 75px; margin-right: 15px"
                />
              </div>
              <div>
                <span>{{ projectData.projectName }}</span>
                <div
                  style="
                    margin-top: 12px;
                    font-size: 14px;
                    display: flex;
                    flex-direction: column;
                  "
                >
                  <div style="margin-bottom: 5px">
                    <span style="margin-right: 8px"
                      >{{ $t("project.versions") }}: {{ item.branch }}</span
                    >
                    <span
                      >{{ $t("project.errorCount") }}:
                      {{
                        item.error_rule_num > 0
                          ? item.error_rule_num
                          : $t("project.noData")
                      }}</span
                    >
                  </div>
                  <span
                    >{{ $t("project.lastScanned") }}: {{ item.scan_time }}</span
                  >
                </div>
              </div>
            </div>
          </el-card>
        </div>
        <div
          v-else
          class="card add-card"
          style="margin-bottom: 15px; border: 1px dashed #d9d9d9"
          @click="openDialog"
        >
          <div class="el-card" style="cursor: pointer; border-radius: 0px">
            <div class="el-card__body">
              <div class="add-branch">
                <i class="el-icon-plus" style="color: #409eff"></i> /
                <i class="el-icon-minus" style="color: #f56c6c"></i>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <!-- 无数据时显示空状态 -->
    <!-- <el-empty v-else :image-size="50"></el-empty> -->
    <!-- 添加分支弹窗 -->
    <el-dialog
      :visible.sync="dialogVisible"
      v-el-drag-dialog
      :close-on-click-modal="false"
      :title="$t('project.addBranch')"
      width="40%"
      @close="closeDialog"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="108px"
        label-position="right"
      >
        <el-form-item
          :label="$t('project.branchName')"
          prop="branch"
          style="margin-bottom: 15px"
        >
          <el-input
            v-model="form.branch"
            clearable
            maxlength="30"
            :placeholder="$t('project.enterBranchName')"
          />
          <el-link type="primary" :underline="false" @click="openCopyDialog"
            >复制已有分支导入</el-link
          >
          <el-dialog
            width="40%"
            title="复制已有分支导入"
            :visible.sync="innerVisible"
            append-to-body
            v-el-drag-dialog
            :close-on-click-modal="false"
            @close="closeCopyDialog"
          >
            <el-form
              ref="copyBranchForm"
              :model="copyBranchForm"
              :rules="copyBranchRules"
              label-width="110px"
            >
              <el-form-item :label="'复制项目'" prop="i_projectId">
                <el-select
                  v-model="copyBranchForm.i_projectId"
                  :placeholder="'选择复制项目'"
                  @change="changeCopyProject"
                  style="width: 100%"
                >
                  <el-option
                    v-for="(item, index) in userProjectList"
                    :key="index"
                    :label="item.projectName"
                    :value="item.projectId"
                  />
                </el-select>
              </el-form-item>
              <el-form-item :label="'复制分支'" prop="i_branch">
                <el-select
                  v-model="copyBranchForm.i_branch"
                  :placeholder="'选择复制分支'"
                  style="width: 100%"
                >
                  <el-option
                    v-for="(item, index) in userBranchList"
                    :key="index"
                    :label="item.branch"
                    :value="item.branch"
                  />
                </el-select>
              </el-form-item>
              <el-form-item :label="'导入项目'" prop="projectId">
                <el-select
                  v-model="copyBranchForm.projectId"
                  :placeholder="'选择导入项目'"
                  style="width: 100%"
                >
                  <el-option
                    v-for="(item, index) in userProjectList"
                    :key="index"
                    :label="item.projectName"
                    :value="item.projectId"
                  />
                </el-select>
              </el-form-item>
              <el-form-item :label="'创建导入分支'" prop="branch">
                <el-input
                  v-model="copyBranchForm.branch"
                  clearable
                  maxlength="30"
                  :placeholder="$t('project.enterBranchName')"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="copyCreateBranch"
                  >立即创建</el-button
                >
                <el-button @click="closeCopyDialog">取消</el-button>
              </el-form-item>
            </el-form>
          </el-dialog>
        </el-form-item>
        <el-form-item
          :label="$t('project.uploadRuleConfig')"
          :rules="{ required: true }"
        >
          <el-upload
            action=""
            accept=".json"
            :on-change="fileChang"
            :on-remove="fileRemove"
            :auto-upload="false"
            :file-list="fileList"
          >
            <el-button size="small" type="primary"
              >{{ $t("project.clickSelect") }} rule_config.json</el-button
            >
            <div slot="tip" style="font-size: 12px; color: #606266">
              {{ $t("project.uploadTips") }}
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-popconfirm
          icon="el-icon-info"
          icon-color="red"
          placement="top"
          :title="$t('project.isDeleteBranch')"
          @confirm="delBranch"
        >
          <el-button slot="reference" type="danger">{{
            $t("project.delete")
          }}</el-button>
        </el-popconfirm>
        <el-button
          style="margin-left: 10px"
          type="primary"
          @click="addBranch"
          >{{ $t("project.add") }}</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import UploadReport from "./UploadReport.vue";
import elDragDialog from "@/directive/el-drag-dialog";
import {
  getUserProjectList,
  getProjectBranchs,
  createBranch,
  deleteBranch,
  importBranch,
} from "@/api/project";

export default {
  components: { UploadReport },
  directives: { elDragDialog },
  props: ["projectData", "branchsData", "spanNum"], // 接受对应项目的所有版本数据
  data() {
    return {
      baseUrl: process.env.VUE_APP_BASE_API, //图片基础路径

      dialogVisible: false,
      form: {
        branch: "",
      },
      fileList: [],
      rules: {
        branch: [
          {
            required: true,
            message: this.$t("project.enterBranchName"),
            trigger: "blur",
          },
        ],
      },

      innerVisible: false, //复制已有分支项目弹窗
      userProjectList: [],
      userBranchList: [],
      copyBranchForm: {
        i_projectId: void 0,
        i_branch: "",
        projectId: void 0,
        branch: "",
      },
      copyBranchRules: {
        i_projectId: [
          {
            required: true,
            message: "请选择项目",
            trigger: "change",
          },
        ],
        i_branch: [
          {
            required: true,
            message: "请选择分支",
            trigger: "change",
          },
        ],
        projectId: [
          {
            required: true,
            message: "请选择导入项目",
            trigger: "change",
          },
        ],
        branch: [
          {
            required: true,
            message: "请输入分支名称",
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {
    // 文件上传时的钩子
    fileChang(file, fileList) {
      // 判断文件名是否重复
      var count = 0;
      fileList.forEach((item, idx) => {
        if (file.name == item.name) {
          count++;
          if (count === 2) {
            setTimeout(() => {
              this.$message({
                message: file.name + this.$t("project.existed"),
                type: "info",
              });
            }, 10);
            fileList.pop(); // 相同则删除新增进来的文件
          }
        }
      });

      this.fileList = fileList;
    },
    fileRemove(file, fileList, name) {
      this.fileList = fileList;
    },

    openDialog() {
      if(this.projectData.projectName == '通用项目') return
      this.dialogVisible = true;
    },
    closeDialog() {
      this.dialogVisible = false;
      this.$refs.form.resetFields();
      this.fileList = [];
    },
    // 添加项目分支
    async addBranch() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          if (this.fileList.length === 0) {
            return this.$message.warning(this.$t("project.selectFiles"));
          }
          // 下面的代码将创建一个空的FormData对象:
          const formData = new FormData();
          // 使用FormData.append来添加键/值对到表单里面；
          this.fileList.forEach((file) => {
            formData.append("json", file.raw);
          });
          // 添加自定义参数
          formData.append("projectId", this.projectData.projectId);
          formData.append("branch", this.form.branch);
          // 发送添加分支请求
          let { code, msg } = await createBranch(formData);
          if (code === 200) {
            this.$message.success(this.$t("project.addBranchSuccess"));
            this.closeDialog();
            this.getProjectBranchs();
          } else if (code === 316) {
            this.$message.warning(this.$t("project.branchExists"));
          } else if (code === 306) {
            this.$message.error(this.$t("project.wrongFormat"));
            this.fileList = [];
          } else {
            this.$message.error(msg);
          }
        }
      });
    },
    // 删除项目分支
    delBranch() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          let params = {
            projectId: this.projectData.projectId,
            branch: this.form.branch,
          };
          let { code, msg } = await deleteBranch(params);
          if (code === 200) {
            this.$message.success(msg);
            this.closeDialog();
            this.getProjectBranchs();
          } else {
            this.$message.error(msg);
          }
        }
      });
    },
    // 打开复制分支弹窗
    openCopyDialog() {
      this.getUserProjectList();
      this.getBranchs(this.projectData.projectId);
      this.copyBranchForm.i_projectId = this.projectData.projectId;
      this.copyBranchForm.projectId = this.projectData.projectId;
      this.innerVisible = true;
    },
    closeCopyDialog() {
      this.innerVisible = false;
    },
    // 获取所有项目
    async getUserProjectList() {
      let { code, projectList, dataLen } = await getUserProjectList();
      if (code === 200) {
        this.userProjectList = projectList;
      }
    },
    async getBranchs(projectId) {
      let { code, data } = await getProjectBranchs({ projectId });
      if (code === 200) {
        this.userBranchList = data;
      }
    },
    changeCopyProject(projectId) {
      this.copyBranchForm.i_branch = "";
      this.getBranchs(projectId);
    },
    copyCreateBranch() {
      this.$refs.copyBranchForm.validate(async (valid) => {
        if (valid) {
          // console.log(this.copyBranchForm);
          let { code, msg } = await importBranch(this.copyBranchForm);
          if (code !== 200) return this.$message.error(msg);
          this.$message.success(msg);
          this.$refs.copyBranchForm.resetFields();
          this.closeCopyDialog();
          this.closeDialog();
          this.getProjectBranchs();
        }
      });
    },

    // 获取项目所有分支
    getProjectBranchs() {
      this.$parent.getProjectBranchs(this.projectData.projectId);
    },

    // 跳转到项目报告
    async goProjectDetail(item) {     
      // if(this.projectData.projectName == '通用项目') return
      this.$store.commit(
        "project/setProjectName",
        this.projectData.projectName
      );
      this.$store.commit("project/setProjectBranch", item.branch);
      let params = {
        projectId: this.projectData.projectId,
        branch: item.branch,
      };
      this.$router.push({
        path: "/prodetail",
        query: params,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.el-page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.add-card:hover {
  border-color: #409eff !important;
}
.add-branch {
  padding: 22px;
  text-align: center;
  font-size: 28px;
  color: #8c939d;
}
</style>
