<template>
  <div class="app-container">
    <div class="report-detail">
      <el-page-header
        :content="$t('reportDetail.reportDetail')"
        :title="$t('reportDetail.backToProjectReport')"
        @back="$router.back()"
      />
      <el-divider />
      <el-button
        style="position: absolute; top: 18px; right: 15px"
        type="danger"
        @click="deleteReport"
      >
        删除报告
      </el-button>
      <!-- 分类tab -->
      <div>
        <el-tabs v-model="activeName" @tab-click="tabChange">
          <el-tab-pane label="资源类型" name="assets"> </el-tab-pane>
          <el-tab-pane label="报错类型" name="error"> </el-tab-pane>
        </el-tabs>
      </div>
      <!-- 资源分类 -->
      <div v-if="activeName === 'assets'">
        <div>
          <el-descriptions
            :title="$t('reportDetail.reportDetail')"
            :column="2"
            style="font-size: 16px"
          >
            <el-descriptions-item :label="$t('reportDetail.testId')">{{
              $route.query.reportId
            }}</el-descriptions-item>
            <el-descriptions-item :label="$t('reportDetail.scanTime')">{{
              reportDetail.scan_time
            }}</el-descriptions-item>
            <el-descriptions-item :label="$t('reportDetail.testVersion')">{{
              reportDetail.branch
            }}</el-descriptions-item>
            <el-descriptions-item :label="$t('reportDetail.errorCount')">{{
              reportDataLen
            }}</el-descriptions-item>
          </el-descriptions>
        </div>
        <div style="margin-top: 20px">
          <report-detail
            :tableTitle="$t('reportDetail.reportList')"
            :reportData="reportData"
          />
        </div>
      </div>
      <!-- 报错类型分类 -->
      <div v-if="activeName === 'error'">
       <ErrorFilterTable></ErrorFilterTable>
      </div>
    </div>
    <!-- 返回顶部组件 -->
    <el-tooltip placement="top" :content="$t('backToTop')">
      <back-to-top
        :visibility-height="300"
        :back-position="50"
        transition-name="fade"
      ></back-to-top>
    </el-tooltip>
  </div>
</template>

<script>
import ReportDetail from "./components/ReportDetail.vue";
import { getReportDetail, deleteReport,getReportDetailByError } from "@/api/project";
import BackToTop from "@/components/BackToTop";
import ErrorFilterTable from './components/errorFilterTable.vue'
export default {
  components: { ReportDetail, BackToTop,ErrorFilterTable },
  data() {
    return {
      reportDetail: {},
      activeName: "assets",
      reportData: [
        // {
        //   id: 1,
        //   type: "SkeletalMesh",
        //   name: "DefaultSkeletal",
        //   path: "/Engine/EditorMeshes/SkeletalMesh/DefaultSkeletalMesh.DefaultSkeletalMesh",
        //   error_rule: "['Skeletal Mesh(蒙皮网络)必须以MD_开头']",
        // },
        // {
        //   id: 2,
        //   type: "SkeletalMesh",
        //   name: "P_hero_langren_sz001_body_skin",
        //   path: "/Engine/EditorMeshes/SkeletalMesh/DefaultSkeletalMesh.DefaultSkeletalMesh",
        //   error_rule: "['Skeletal Mesh(蒙皮网络)必须以MD_开头']",
        // },
        // {
        //   id: 3,
        //   type: "SkeletalMesh",
        //   name: "DefaultSkeletal",
        //   path: "/Engine/EditorMeshes/SkeletalMesh/DefaultSkeletalMesh.DefaultSkeletalMesh",
        //   error_rule: "['Skeletal Mesh(蒙皮网络)必须以MD_开头']",
        // },
        // {
        //   id: 4,
        //   type: "SkeletalMesh",
        //   name: "DefaultSkeletal",
        //   path: "/Engine/EditorMeshes/SkeletalMesh/DefaultSkeletalMesh.DefaultSkeletalMesh",
        //   error_rule: "['Skeletal Mesh(蒙皮网络)必须以MD_开头']",
        // },
      ],
      reportDataLen: 0,
    };
  },
  mounted() {
    this.getReportDetail();
  },

  methods: {
    // 获取报告详情
    async getReportDetail() {
      let id = this.$route.query.reportId;
      if (!id) {
        this.$router.push("/project/index");
      }
      const params = {
        projectId: this.$route.query.projectId,
        id,
        pageNum: 1,
        pageSize: 30,
      };
      const { code, branch, data, dataLen } = await getReportDetail(params);
      if (code === 200) {
        this.reportDetail = branch;
        this.reportData = data;
        this.reportDataLen = dataLen;
      }
    },
    deleteReport() {
      this.$confirm("确定删除该报告吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const params = {
          projectId: this.$route.query.projectId,
          branch: this.reportDetail.branch,
          scan_time: this.reportDetail.scan_time,
          is_delete: true,
        };
        const { code, msg } = await deleteReport(params);
        if (code !== 200) return this.$message.error(msg);
        this.$message({
          type: "success",
          message: "删除成功!",
        });
        this.$router.back();
      });
    },
    // 切换tab
    tabChange(val){
      console.log('val',val);
      
    }
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  .report-detail {
    background-color: #fff;
    padding: 15px;
    padding-top: 25px;
    position: relative;
  }
}
</style>
