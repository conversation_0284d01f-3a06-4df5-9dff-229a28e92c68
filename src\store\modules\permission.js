// import { asyncRoutes, constantRoutes } from '@/router'
import { constantRoutes } from '@/router'

// 后端返回的嵌套路由表 转一维数组，
// 转换后用于与总路由表进行比较，后端若返回一维数组则直接交由下面filterAsyncRoutes处理
function MultidimensionalToOnedimensional(routesMap) {
  const filterRoutesMap = []
  !(function fn(routesMap) {
    routesMap.forEach(route => {
      const tmp = {}
      for (const key in route) {
        if (Object.hasOwnProperty.call(route, key)) {
          if (key !== 'children') {
            tmp[key] = route[key]
          } else if (key === 'children') {
            fn(route[key])
          }
        }
      }
      filterRoutesMap.push(tmp)
    })
  }(routesMap))
  return filterRoutesMap
}

/**
 * Use meta.role to determine if the current user has permission
 * @param roles
 * @param route
 */
// function hasPermission(roles, route) {
//   if (route.meta && route.meta.roles) {
//     return roles.some(role => route.meta.roles.includes(role))
//   } else {
//     return true
//   }
// }

/**
 * Filter asynchronous routing tables by recursion
 * @param routes asyncRoutes
 * @param roles
 */
// export function filterAsyncRoutes(routes, roles) {
//   const res = []

//   routes.forEach(route => {
//     const tmp = { ...route }
//     if (hasPermission(roles, tmp)) {
//       if (tmp.children) {
//         tmp.children = filterAsyncRoutes(tmp.children, roles)
//       }
//       res.push(tmp)
//     }
//   })

//   return res
// }
// 比对过滤路由表，生成最终可访问的路由表
export function filterAsyncRoutes(routes, filterRoutesMap) {
  const accessedRoutes = []
  routes.forEach(route => {
    const tmp = {}
    if (filterRoutesMap.some(a => a.path == route.path)) {
      for (const key in route) {
        if (Object.hasOwnProperty.call(route, key)) {
          if (key !== 'children') {
            tmp[key] = route[key]
          } else if (key === 'children') {
            const tmpC = filterAsyncRoutes(route[key], filterRoutesMap);
            (tmpC.length > 0) && (tmp.children = tmpC)
          }
        }
      }
    }
    tmp.path && accessedRoutes.push(tmp)
  })
  return accessedRoutes
}

const state = {
  routes: [],
  addRoutes: []
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes
    state.routes = constantRoutes.concat(routes)
    // console.log(state.routes);
  },
  //  重置路由，用于退出登录操作
  RESET_STATE: state => {
    state.addRoutes = []
    state.routes = []
  }
}

const actions = {
  // 生成可访问路由表
  generateRoutes({ commit }, { asyncRoutes, routesMap }) {
    return new Promise(resolve => {
      // if (routesMap.at(-1).path !== '*') {
      //   routesMap.push({ path: '*', redirect: '/404', hidden: true })
      // }
      const filterRoutesMap = MultidimensionalToOnedimensional(routesMap)
      // console.log(filterRoutesMap);
      const accessedRoutes = filterAsyncRoutes(asyncRoutes, filterRoutesMap)
      commit('SET_ROUTES', accessedRoutes)
      // console.log(accessedRoutes);
      resolve(accessedRoutes)
    })
  },
  //  重置路由，用于退出登录操作
  resetState({ commit }) {
    return new Promise(resolve => {
      commit('RESET_STATE')
      resolve()
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
