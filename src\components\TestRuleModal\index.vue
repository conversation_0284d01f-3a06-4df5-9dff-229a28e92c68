<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="$t('testRule.testRule')"
      :visible.sync="dialogVisible"
      width="75%"
      @close="closeDialog"
    >
      <el-table :data="testRuleData" max-height="560" style="width: 100%">
        <el-table-column type="index" label="#" width="50" />
        <el-table-column prop="name" label="name" />
        <el-table-column prop="type" label="type" width="200" />
        <el-table-column prop="path" label="path" />
        <el-table-column prop="errorList" :label="$t('testRule.errorList')" />
        <el-table-column
          fixed="right"
          :label="$t('testRule.operate')"
          width="100"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="handleClick(scope.row)"
              >{{ $t("testRule.checkMore") }}</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <!-- <pagination
        style="margin-top: 0; padding: 10px 20px"
        :total="testRuleData.length"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="pageSwitch"
      /> -->
      <!-- <span slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">退 出</el-button>
        <el-button type="primary" @click="closeDialog">确 定</el-button>
      </span> -->
      <!-- 查看更多弹窗 -->
      <el-dialog
        v-el-drag-dialog
        :visible.sync="dialogMoreVisible"
        append-to-body
        :title="$t('testRule.dataDetails')"
        width="60%"
      >
        <el-button
          v-if="projectType !== 'table'"
          type="text"
          @click="fieldName = !fieldName"
          >{{ $t("reportDetail.switchFieldName") }}</el-button
        >
        <el-descriptions :column="2">
          <el-descriptions-item
            v-for="(value, key) in fieldName ? cnTestData : enTestData"
            :key="key"
            :label-style="{ fontWeight: '600' }"
            :label="key"
            >{{ value }}</el-descriptions-item
          >
        </el-descriptions>
      </el-dialog>
    </el-dialog>
  </div>
</template>

<script>
import elDragDialog from "@/directive/el-drag-dialog";
import Pagination from "@/components/Pagination/index.vue";
import { mapGetters } from "vuex";

export default {
  components: { Pagination },
  props: ["projectType", "testRuleVisible", "testRuleData"],
  directives: { elDragDialog },
  data() {
    return {
      dialogVisible: this.testRuleVisible,
      enTestData: {},
      cnTestData: {},
      dialogMoreVisible: false,
      fieldName: true, //详情弹窗的字段名语言切换 默认true中文

      total: 0,
      listQuery: {
        page: 1,
        limit: 10,
      },
    };
  },
  computed: {
    ...mapGetters(["ruleConfig"]),
  },
  watch: {
    testRuleVisible(val) {
      this.dialogVisible = val;
    },
  },
  methods: {
    closeDialog() {
      this.$emit("closeTestRule", false);
    },
    handleClick(row) {
      this.dialogMoreVisible = true;
      this.enTestData = row; //保存英文字段弹窗详细数据
      if (this.projectType === "table") {
        this.fieldName = false;
        return;
      }
      // 处理中文字段名称
      let data = row;
      let stypeIndex = (this.ruleConfig || []).findIndex(
        (item) => item.type === data.type
      );
      let values = this.ruleConfig[stypeIndex].values;
      let cnName = {}; //保存中文名称key
      values.forEach((item) => {
        cnName[item.name] = item.cn ? item.cn : item.name;
      });
      cnName.scan_time = "扫描时间";
      cnName.errorList = "报错列表";
      cnName.type = "类型";
      cnName.branch = "分支";
      cnName.size = "大小";
      let objs = Object.keys(data).reduce((newData, key) => {
        let newKey = cnName[key] || key;
        newData[newKey] = data[key];
        return newData;
      }, {});
      this.cnTestData = objs; //保存中文字段弹窗展示数据
    },
    pageSwitch() {},
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 5px 20px 30px 20px;
}
</style>
