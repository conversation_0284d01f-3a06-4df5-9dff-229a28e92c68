<template>
  <div>
    <!-- 根据检测类型显示不同的组件 -->
    <artComponent v-if="selectedCheckType === 1"></artComponent>
    <tableComponent v-else-if="selectedCheckType === 2"></tableComponent>
    <!-- 默认显示空状态组件 -->
    <emptyState v-else></emptyState>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import artComponent from './artComponent.vue'
import tableComponent from './tableProjectDetails/index.vue'
import emptyState from './emptyState.vue'

export default {
  components: {
    artComponent,
    tableComponent,
    emptyState
  },
  name: "DataOverview",
  computed: {
    ...mapGetters(['selectedCheckType'])
  },
  data() {
    return {

    }
  }
  // 在页面离开时记录滚动位置
}
</script>

<style lang="scss" scoped>
</style>
