<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :title="dialogType === 'edit' ? '编辑项目' : '新增项目'"
    width="600px"
    @close="handleClose"
  >
    <el-form
      ref="projectForm"
      :model="formData"
      :rules="rules"
      label-width="120px"
      label-position="right"
    >
      <!-- 项目名称 -->
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="formData.projectName"
          maxlength="30"
          placeholder="请输入项目名称"
        />
      </el-form-item>

      <!-- 项目logo -->
      <el-form-item label="项目logo">
        <el-upload
          class="avatar-uploader"
          action=""
          accept=".jpg,.jpeg,.png,.gif,.webp"
          :show-file-list="false"
          :auto-upload="false"
          :on-change="handleFileChange"
        >
          <template v-if="imageUrl">
            <img :src="imageUrl" class="avatar" />
            <div class="upload-handle" @click.stop>
              <div class="handle-icon" @click="editImg">
                <i class="el-icon-edit-outline" style="padding-bottom: 4px" />
              </div>
              <div class="handle-icon" @click="viewImg">
                <i class="el-icon-zoom-in" />
              </div>
              <div class="handle-icon" @click="deleteImg">
                <i class="el-icon-delete" />
              </div>
            </div>
          </template>
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
      </el-form-item>

      <!-- 项目描述 -->
      <el-form-item label="项目描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="4"
          maxlength="200"
          placeholder="请输入项目描述"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="loading">
        确定
      </el-button>
    </div>

    <!-- 查看项目Icon -->
    <el-image-viewer
      v-if="imgViewVisible"
      style="z-index: 2006"
      :on-close="closeImgViewer"
      :url-list="[imageUrl]"
    />
  </el-dialog>
</template>

<script>
import { createProject, editProject } from '@/api/permission-project'
import { uploadSculpture } from '@/api/upload'

export default {
  name: 'ProjectDialog',
  components: {
    'el-image-viewer': () => import('element-ui/packages/image/src/image-viewer')
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    dialogType: {
      type: String,
      default: 'new' // 'new' | 'edit'
    },
    projectData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      formData: {
        projectName: '',
        description: ''
      },
      imageUrl: '',
      uploadFile: null,
      imgViewVisible: false,
      baseUrl: process.env.VUE_APP_BASE_API,
      rules: {
        projectName: [
          {
            required: true,
            message: '请输入项目名称',
            trigger: 'blur'
          }
        ]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initFormData()
      }
    }
  },
  methods: {
    // 初始化表单数据
    initFormData() {
      if (this.dialogType === 'edit' && this.projectData.projectId) {
        this.formData = {
          projectName: this.projectData.projectName || '',
          description: this.projectData.description || ''
        }
        // 设置项目logo
        this.imageUrl = `${this.baseUrl}/media/sculpture/${this.projectData.projectId}.png?time=${Date.now()}`
      } else {
        this.formData = {
          projectName: '',
          description: ''
        }
        this.imageUrl = ''
        this.uploadFile = null
      }
    },

    // 文件选择
    handleFileChange(file) {
      this.uploadFile = file.raw
      this.imageUrl = URL.createObjectURL(file.raw)
    },

    // 编辑图片
    editImg() {
      // 触发文件选择
      this.$el.querySelector('.avatar-uploader input').click()
    },

    // 查看图片
    viewImg() {
      this.imgViewVisible = true
    },

    // 删除图片
    deleteImg() {
      this.imageUrl = ''
      this.uploadFile = null
    },

    // 关闭图片查看器
    closeImgViewer() {
      this.imgViewVisible = false
    },

    // 关闭弹窗
    handleClose() {
      this.dialogVisible = false
      this.$refs.projectForm.resetFields()
      this.imageUrl = ''
      this.uploadFile = null
    },

    // 确认操作
    async handleConfirm() {
      this.$refs.projectForm.validate(async (valid) => {
        if (valid) {
          this.loading = true
          try {
            if (this.dialogType === 'edit') {
              await this.updateProject()
            } else {
              await this.createNewProject()
            }
            this.$message.success(this.dialogType === 'edit' ? '编辑成功' : '新增成功')
            this.$emit('confirm')
            this.handleClose()
          } catch (error) {
            console.error('操作失败:', error)
            this.$message.error('操作失败')
          } finally {
            this.loading = false
          }
        }
      })
    },

    // 创建新项目
    async createNewProject() {
      const params = {
        projectName: this.formData.projectName,
        description: this.formData.description
      }
      
      const res = await createProject(params)
      if (res.code === 200) {
        // 如果有上传的图片，上传项目logo
        if (this.uploadFile) {
          await this.uploadProjectLogo(res.projectId)
        }
      } else {
        throw new Error(res.msg || '创建项目失败')
      }
    },

    // 更新项目
    async updateProject() {
      const params = {
        projectId: this.projectData.projectId,
        projectName: this.formData.projectName,
        description: this.formData.description
      }
      
      const res = await editProject(params)
      if (res.code === 200) {
        // 如果有上传的图片，上传项目logo
        if (this.uploadFile) {
          await this.uploadProjectLogo(this.projectData.projectId)
        }
      } else {
        throw new Error(res.msg || '更新项目失败')
      }
    },

    // 上传项目logo
    async uploadProjectLogo(projectId) {
      if (!this.uploadFile) return
      
      const formData = new FormData()
      formData.append('photo', this.uploadFile)
      formData.append('projectId', projectId)
      
      const res = await uploadSculpture(formData)
      if (res.code !== 200) {
        throw new Error(res.msg || '上传logo失败')
      }
    }
  }
}
</script>

<style scoped lang="scss">
.avatar-uploader {
  position: relative;
  
  .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:hover {
      border-color: #409EFF;
    }
  }
  
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
  }
  
  .avatar {
    width: 100px;
    height: 100px;
    object-fit: cover;
    display: block;
  }
  
  .upload-handle {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    opacity: 0;
    transition: opacity 0.3s;
    
    &:hover {
      opacity: 1;
    }
    
    .handle-icon {
      width: 24px;
      height: 24px;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      
      &:hover {
        background: rgba(255, 255, 255, 1);
      }
      
      i {
        font-size: 12px;
        color: #333;
      }
    }
  }
}
</style>
