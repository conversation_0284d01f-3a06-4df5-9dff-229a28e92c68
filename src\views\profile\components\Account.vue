<template>
  <el-form style="width: 35%">
    <el-form-item :label="$t('profile.userName')">
      <el-input ref="userName" v-model.trim="user.name" />
    </el-form-item>
    <el-form-item ref="nickName" :label="$t('profile.nickName')">
      <el-input v-model.trim="user.nickName" />
    </el-form-item>
    <!-- <el-form-item label="个人简介：">
      <el-input
        v-model.trim="user.resume"
        type="textarea"
        :rows="3"
        maxlength="60"
        show-word-limit
      />
    </el-form-item> -->
    <el-form-item>
      <el-button type="primary" @click="submit">{{
        $t("profile.submit")
      }}</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { updateUserInfo } from "@/api/user";
import { setToken } from "@/utils/auth";

export default {
  props: {
    user: {
      type: Object,
      default: () => {
        return {
          name: "",
          nickName: "",
        };
      },
    },
  },
  methods: {
    submit() {
      this.$confirm(this.$t("profile.isSubmit"), this.$t("profile.tips"), {
        type: "warning",
      })
        .then(async () => {
          let params = {
            userName: this.user.name,
            nickName: this.user.nickName,
          };
          let { code, data } = await updateUserInfo(params);
          if (code === 200) {
            setToken(data.token);
            this.$store.commit(
              "user/SET_NAME",
              this.user.nickName || this.user.name
            );
            this.$message({
              message: this.$t("profile.submitSuccess"),
              type: "success",
            });
            return;
          }
          if (code === 206) {
            this.$message({
              type: "error",
              message: this.$t("profile.userNameExists"),
            });
            this.$refs.userName.focus();
          }
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消",
          });
        });
    },
  },
};
</script>
