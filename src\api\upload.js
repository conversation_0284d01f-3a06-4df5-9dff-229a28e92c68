import request from "@/utils/request";

export function uploadAvatar(data) {
  return request({
    url: "/upLoad_avatar",
    method: "post",
    data,
    headers: { "Content-Type": "multipart/form-data" },
  });
}

export function uploadReport(data) {
  return request({
    url: "/upload_res",
    method: "post",
    data,
    headers: { "Content-Type": "multipart/form-data" },
  });
}

export function uploadSculpture(data) {
  return request({
    url: "/upLoad_sculpture",
    method: "post",
    data,
    headers: { "Content-Type": "multipart/form-data" },
  });
}
// 批量导入规则
export function uploadRules(data) {
  return request({
    url: "/upload_rules",
    method: "post",
    data,
    headers: { "Content-Type": "multipart/form-data" },
  });
}
