import request from '@/utils/request'
import Qs from 'qs'

export function getAllUser(query) {
  return request({
    url: '/get_all_user',
    method: 'get',
    params: query
  })
}

export function createUser(data) {
  data = Qs.stringify(data)
  return request({
    url: `/create_user`,
    method: 'post',
    data
  })
}

export function editUser(data) {
  data = Qs.stringify(data)
  return request({
    url: `/edit_user`,
    method: 'post',
    data
  })
}

export function isEnabledUser(data) {
  data = Qs.stringify(data)
  return request({
    url: `/isEnabled_user`,
    method: 'post',
    data
  })
}

export function updatePassword(data) {
  data = Qs.stringify(data)
  return request({
    url: `/update_password`,
    method: 'post',
    data
  })
}

export function deleteUser(data) {
  data = Qs.stringify(data)
  return request({
    url: `/delete_user`,
    method: 'post',
    data
  })
}

export function getUserProjectList(query) {
  return request({
    url: '/get_projectList_user',
    method: 'get',
    params: query
  })
}
