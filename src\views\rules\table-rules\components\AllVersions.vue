<template>
  <div>
    <div class="el-page-header">
      <div class="el-page-header__content">
        {{ "分支信息" }}
      </div>
    </div>
    <el-divider />

    <el-row :gutter="20">
      <el-col v-for="(item, i) in branchsData" :key="i" :span="spanNum">
        <div
          v-if="Object.keys(item).length > 0"
          class="card"
          style="margin-bottom: 15px"
          @click="goProjectDetail(item)"
        >
          <el-card shadow="hover" style="cursor: pointer; border-radius: 0">
            <div
              style="display: flex; justify-items: center; align-items: center"
            >
              <div>
                <img
                  :src="`${baseUrl}/media/sculpture/${
                    projectData.projectId
                  }.png?time=${Date.now()}`"
                  alt=""
                  style="width: 75px; height: 75px; margin-right: 15px"
                />
              </div>
              <div>
                <span>{{ item.branch }}</span>
                <div
                  style="
                    margin-top: 12px;
                    font-size: 14px;
                    display: flex;
                    flex-direction: column;
                  "
                >
                  <div style="margin-bottom: 5px">
                    <span style="margin-right: 8px"
                      >{{ $t("project.versions") }}: {{ item.branch }}</span
                    >
                    <span
                      >{{ "拦截数" }}:
                      {{
                        item.effective_count > 0
                          ? item.effective_count
                          : $t("project.noData")
                      }}</span
                    >
                  </div>
                  <span>{{ "最近检查时间" }}: {{ item.scan_time }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </div>
        <div
          v-else
          class="card add-card"
          style="margin-bottom: 15px; border: 1px dashed #d9d9d9"
          @click="openDialog"
        >
          <div class="el-card" style="cursor: pointer; border-radius: 0px">
            <div class="el-card__body">
              <div class="add-branch">
                <i class="el-icon-plus" style="color: #409eff" /> /
                <i class="el-icon-minus" style="color: #f56c6c" />
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <!-- 无数据时显示空状态 -->
    <!-- <el-empty v-else :image-size="50"></el-empty> -->
    <!-- 添加分支弹窗 -->
    <el-dialog
      v-el-drag-dialog
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      :title="$t('project.addBranch')"
      width="35%"
      @close="closeDialog"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="80px"
        label-position="right"
      >
        <el-form-item :label="$t('project.branchName')" prop="branch">
          <el-input
            v-model="form.branch"
            maxlength="30"
            :placeholder="$t('project.enterBranchName')"
          />
        </el-form-item>
        <el-divider content-position="left">表格获取信息填写</el-divider>
        <el-form-item label="仓库类型" prop="type">
          <el-select
            v-model="form.type"
            placeholder="请选择类型"
            clearable
            style="width: 150px"
          >
            <el-option label="git" value="git" />
            <el-option label="svn" value="svn" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="form.type" label="仓库链接" prop="url">
          <el-input v-model="form.url" placeholder="请填写仓库链接" />
        </el-form-item>
        <el-form-item
          v-if="form.type === 'git'"
          label="分支名称"
          prop="gitbranch"
        >
          <el-input v-model="form.gitbranch" placeholder="请填写仓库分支名称" />
        </el-form-item>
        <el-form-item v-if="form.type" label="用户名" prop="user">
          <el-input v-model="form.user" placeholder="请填写仓库登录用户名" />
        </el-form-item>
        <el-form-item v-if="form.type" label="用户密码" prop="password">
          <el-input
            v-model="form.password"
            show-password
            placeholder="请填写仓库登录密码"
          />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-popconfirm
          icon="el-icon-info"
          icon-color="red"
          placement="top"
          :title="$t('project.isDeleteBranch')"
          @confirm="delBranch"
        >
          <el-button slot="reference" type="danger">{{
            $t("project.delete")
          }}</el-button>
        </el-popconfirm>
        <el-button
          style="margin-left: 10px"
          type="primary"
          @click="addBranch"
          >{{ $t("project.add") }}</el-button
        >
        <el-button type="" @click="closeDialog">{{
          $t("project.cancel")
        }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import elDragDialog from "@/directive/el-drag-dialog";
import { createTableProjectBranch, deleteBranch } from "@/api/project";

export default {
  directives: { elDragDialog },
  props: ["projectData", "branchsData", "spanNum"], // 接受对应项目的所有版本数据
  data() {
    return {
      baseUrl: process.env.VUE_APP_BASE_API, // 图片基础路径

      dialogVisible: false,
      form: {
        branch: "",
        type: "",
        url: "",
        gitbranch: "",
        user: "",
        password: "",
      },
      fileList: [],
      rules: {
        branch: [
          {
            required: true,
            message: this.$t("project.enterBranchName"),
            trigger: "blur",
          },
        ],
        url: [{ required: true, message: "请输入仓库链接", trigger: "blur" }],
        gitbranch: [
          { required: true, message: "请输入仓库链接", trigger: "blur" },
        ],
        user: [{ required: true, message: "请输入用户名", trigger: "blur" }],
        password: [
          { required: true, message: "请输入用户密码", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    // 文件上传时的钩子
    fileChang(file, fileList) {
      // 判断文件名是否重复
      var count = 0;
      fileList.forEach((item, idx) => {
        if (file.name == item.name) {
          count++;
          if (count === 2) {
            setTimeout(() => {
              this.$message({
                message: file.name + this.$t("project.existed"),
                type: "info",
              });
            }, 10);
            fileList.pop(); // 相同则删除新增进来的文件
          }
        }
      });

      this.fileList = fileList;
    },
    fileRemove(file, fileList, name) {
      this.fileList = fileList;
    },

    openDialog() {
      this.dialogVisible = true;
    },
    closeDialog() {
      this.dialogVisible = false;
      this.$refs.form.resetFields();
      this.fileList = [];
    },

    // 添加项目分支
    async addBranch() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          const params = {
            isExcel: true,
            projectId: this.projectData.projectId,
            ...this.form,
          };
          // 发送添加分支请求
          const { code, msg } = await createTableProjectBranch(params);
          if (code === 200) {
            this.$message.success(this.$t("project.addBranchSuccess"));
            this.closeDialog();
            this.getProjectBranchs();
          } else if (code === 316) {
            this.$message.warning(this.$t("project.branchExists"));
          } else if (code === 306) {
            this.$message.error(this.$t("project.wrongFormat"));
            this.fileList = [];
          } else {
            this.$message.error(msg);
          }
        }
      });
    },
    // 删除项目分支
    delBranch() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          const params = {
            projectId: this.projectData.projectId,
            branch: this.form.branch,
          };
          const { code, msg } = await deleteBranch(params);
          if (code === 200) {
            this.$message.success(msg);
            this.closeDialog();
            this.getProjectBranchs();
          } else {
            this.$message.error(msg);
          }
        }
      });
    },

    // 获取项目所有分支
    getProjectBranchs() {
      this.$parent.getProjectBranchs(this.projectData.projectId);
    },

    // 跳转到项目报告
    async goProjectDetail(item) {
      this.$store.commit(
        "project/setProjectName",
        this.projectData.projectName
      );
      this.$store.commit("project/setProjectBranch", item.branch);
      const params = {
        projectId: this.projectData.projectId,
        branch: item.branch,
      };
      this.$router.push({
        path: "/tabledetail",
        query: params,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.el-page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.add-card:hover {
  border-color: #409eff !important;
}
.add-branch {
  padding: 22px;
  text-align: center;
  font-size: 28px;
  color: #8c939d;
}
</style>
