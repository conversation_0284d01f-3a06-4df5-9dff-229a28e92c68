<template>
  <div>
    <h4 style="margin-bottom: 15px">{{ tableTitle }}</h4>
    <el-table
      v-loading="listLoading"
      element-loading-text="数据加载中..."
      :data="
        list.slice(
          listQuery.limit * (listQuery.page - 1),
          listQuery.limit * listQuery.page
        )
      "
      :border="true"
      max-height="500px"
    >
      <el-table-column fixed prop="name" label="name" />
      <el-table-column prop="type" label="type" />
      <el-table-column prop="path" label="path" />
      <el-table-column prop="errorList" :label="$t('reportDetail.errorList')" />
      <el-table-column fixed="right" :label="$t('reportDetail.operation')">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="handleClick(scope.$index)"
            >{{ $t("reportDetail.viewMore") }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页器 -->
    <div style="display: flex; justify-content: end">
      <el-pagination
        style="margin-top: 10px"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="listQuery.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="listQuery.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <!-- 详情弹窗 -->
    <el-dialog
      v-el-drag-dialog
      :visible.sync="dialogTableVisible"
      :title="$t('reportDetail.detailedData')"
      width="60%"
    >
      <el-button type="text" @click="fieldName = !fieldName">{{
        $t("reportDetail.switchFieldName")
      }}</el-button>
      <el-descriptions :column="2">
        <el-descriptions-item
          v-for="(value, key) in fieldName ? cnDialogData : enDialogData"
          :key="key"
          :label-style="{ fontWeight: '600' }"
          :label="key"
          >{{ value }}</el-descriptions-item
        >
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import elDragDialog from "@/directive/el-drag-dialog";
import { getReportStat } from "@/api/project";
import { getRuleConfig } from "@/api/rule";

export default {
  name: "compareTable",
  directives: { elDragDialog },
  props: ["reportData", "reportDataLen", "tableTitle"],
  data() {
    return {
      ruleConfig: [], //保存规则分类

      reportTypeList: [],
      reportType: "",
      listLoading: true,
      total: 0,
      list: [],
      listQuery: {
        page: 1,
        limit: 30,
      },

      cnDialogData: [], //保存中文字段名详细数据
      enDialogData: [], //保存英文字段名详细数据
      dialogTableVisible: false,
      selectType: "",
      searchInput: "", // 搜索关键词
      fieldName: true, //详情弹窗的字段名语言切换 默认true中文

      showTooltip: false,
    };
  },
  watch: {
    reportData(newValue, oldValue) {
      this.list = newValue;
      this.total = this.reportDataLen;
      this.listLoading = false;
    },
  },
  mounted() {
    this.getRuleConfig();
    // this.getReportStat();
    // this.getList();
  },
  methods: {
    handleSizeChange(val) {
      this.listQuery.limit = val;
    },
    handleCurrentChange(val) {
      this.listQuery.page = val;
    },
    // 选择类型
    handleSelect(key) {
      this.reportType = key;
      this.listQuery.page = 1;
      this.listQuery.limit = 30;
      this.getList();
    },

    // 获取规则分类
    async getRuleConfig() {
      let params = {
        projectId: this.$route.query.projectId,
        branch: this.$route.query.branch,
      };
      let { code, json } = await getRuleConfig(params);
      if (code === 200) {
        this.ruleConfig = json;
      }
    },
    // 根据搜索类型和关键词过滤数据
    async getList() {
      this.list = this.reportData;
      this.total = this.reportDataLen;
      this.listLoading = false;
    },
    // 获取数据分类
    async getReportStat() {
      if (this.$route.query.branch) {
        let params = {
          projectId: this.$route.query.projectId,
          branch: this.$route.query.branch,
        };
        let { code, data } = await getReportStat(params);
        if (code === 200) {
          let typeData = [{ type: "All" }];
          data.stat.forEach((item) => {
            if (item.error_rule_num !== 0) typeData.push(item);
          });
          this.reportTypeList = typeData;
          this.reportType = "";
          this.getList();
        }
      }
    },
    // 筛选报告类型
    changeType() {
      this.listQuery.page = 1;
      this.listQuery.limit = 30;
      this.getList();
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.page = 1;
      this.listQuery.limit = 30;
      this.getList();
    },

    // 详细弹窗展示对应数据
    handleClick(i) {
      this.dialogTableVisible = true;
      this.enDialogData = this.list[i]; //保存英文字段弹窗展示数据
      let data = this.list[i];
      // 处理中文字段名称
      let stypeIndex = (this.ruleConfig || []).findIndex(
        (item) => item.type === data.type
      );
      let values = this.ruleConfig[stypeIndex].values;
      let cnName = {}; //保存中文名称key
      values.forEach((item) => {
        cnName[item.name] = item.cn ? item.cn : item.name;
      });
      cnName.scan_time = "扫描时间";
      cnName.errorList = "报错列表";
      cnName.type = "类型";
      cnName.branch = "分支";
      cnName.size = "大小";
      let objs = Object.keys(data).reduce((newData, key) => {
        let newKey = cnName[key] || key;
        newData[newKey] = data[key];
        return newData;
      }, {});
      this.cnDialogData = objs; //保存中文字段弹窗展示数据
    },

    // 分页器页码切换 获取页码和页数
    pageSwitch(val) {
      this.getList();
    },
  },
};
</script>

<style lang="scss" scoped>
.filter-type {
  display: flex;
  vertical-align: middle;
}

.input-with-select .el-input-group__prepend {
  background-color: #fff;
}

::v-deep .el-dialog__body {
  padding: 0 20px 20px 20px;
}

::v-deep .el-menu-item.is-active {
  background-color: #e8f4ff;
}

// ----------修改elementui表格的默认样式-----------
::v-deep .el-table__body-wrapper {
  &::-webkit-scrollbar {
    height: 6px;
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    // 轨道颜色
    background-color: #fff;
  }

  &::-webkit-scrollbar-thumb {
    // 滚动块颜色
    background-color: #e6e9ed;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    // hover高亮
    background-color: #d5d8db;
  }
}

// 解决表格固定列问题
// ::v-deep .el-table__fixed,
// .el-table__fixed-right {
//   height: calc(100% - 7px) !important;
//   box-shadow: -5px -2px 10px rgba(0, 0, 0, 0.12) !important;
//   .el-table__fixed-body-wrapper {
//     height: calc(100% - 36px) !important;
//   }
// }
// 当表格没有滚动条时
::v-deep .el-table__body-wrapper.is-scrolling-none ~ .el-table__fixed-right {
  height: 100% !important;
  bottom: 0 !important;
  box-shadow: none !important;
}

// 当表格有纵向滚动条时
::v-deep .el-table--scrollable-y .el-table__fixed-right {
  right: 6px !important;
}

// 当表格只有横向滚动条，没有纵向滚动条时
::v-deep
  .el-table--scrollable-x:not(.el-table--scrollable-y)
  .el-table__fixed-right {
  right: 0 !important;
}
</style>
