<template>
  <div>
    <!-- 首页模块1 开始-->
    <div class="slider-area">
      <div class="single-sliders slider-height d-flex align-items-center">
        <div class="container position-relative">
          <div class="row align-items-center">
            <div class="col-xl-5 col-lg-6 col-md-6">
              <div class="hero-caption">
                <h1>AssetsLint</h1>
                <P>{{ $t("home.automatedResourceTesting") }}</P>
                <!-- <router-link to="/helpguide" class="btn hero-btn">{{
                  $t("home.clickToHelpGuide")
                }}</router-link> -->
                <!-- <a href="/helpguide/guide1" class="btn hero-btn"
                  >点击前往帮助指引</a
                > -->
              </div>
            </div>
            <!-- 图像 -->
            <div class="hero-shape">
              <img src="@/assets/index_image/hero-img.png" alt="">
            </div>
          </div>
        </div>
      </div>
      <div class=".collection" style="position: absolute; top: 50%; right: 25%">
        <div class="single-question">
          <!-- 视频icon -->
          <div class="video-icon">
            <a class="popup-video btn-icon" data-animation="bounceIn" data-delay=".4s" @click="playVideo"><i
              class="el-icon-caret-right"
            /></a>
          </div>
        </div>
      </div>
    </div>
    <!-- 首页模块1 结束 -->
    <!-- 视频弹窗开始 -->
    <el-dialog :title="$t('home.demoVideo')" :visible.sync="dialogPlay" width="60%" @close="closeDialog">
      <video ref="dialogVideo" src="@/assets/video/video.mp4" controls autoplay class="video" width="100%" />
    </el-dialog>
    <!-- 视频弹窗结束 -->
    <!-- 首页模块2 开始 -->
    <section class="categories-area section-padding">
      <div class="container">
        <div class="row justify-content-center">
          <div class="col-xl-6 col-lg-6 col-md-10">
            <!-- Section Tittle -->
            <div class="section-tittle text-center mb-25">
              <!-- <span>系统模块</span> -->
              <h2>{{ $t("home.platformModules") }}</h2>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-4 col-md-6 col-sm-6">
            <div
              class="single-cat text-center mb-50"
              @click="$router.push('/resourcedetect/project')"
            >
              <div class="cat-icon">
                <img src="@/assets/index_image/icon/services1.svg" alt="">
              </div>
              <div class="cat-cap">
                <h5>
                  <router-link to="/resourcedetect/project">{{
                    $t("home.myProjects")
                  }}</router-link>
                </h5>
                <p>{{ $t("home.checkAllProject") }}</p>
              </div>
            </div>
          </div>
          <div class="col-lg-4 col-md-6 col-sm-6">
            <div
              class="single-cat text-center mb-50"
              @click="$router.push('/resourcedetect/rulelist')"
            >
              <div class="cat-icon">
                <img src="@/assets/index_image/icon/services2.svg" alt="">
              </div>
              <div class="cat-cap">
                <h5>
                  <router-link to="/resourcedetect/rulelist">{{
                    $t("home.rulesList")
                  }}</router-link>
                </h5>
                <p>{{ $t("home.checkAllrules") }}</p>
              </div>
            </div>
          </div>
          <div class="col-lg-4 col-md-6 col-sm-6">
            <div
              class="single-cat text-center mb-50"
              @click="$router.push('/resourcedetect/rulecreate')"
            >
              <div class="cat-icon">
                <img src="@/assets/index_image/icon/services3.svg" alt="">
              </div>
              <div class="cat-cap">
                <h5>
                  <router-link to="/resourcedetect/rulecreate">{{
                    $t("home.createRule")
                  }}</router-link>
                </h5>
                <p>{{ $t("home.createNewRule") }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- 首页模块2 结束 -->
    <!-- 首页模块3 开始 -->
    <section class="about-area2 section-padding white-bg">
      <div class="container">
        <div class="row align-items-center justify-content-between">
          <div class="col-xl-5 col-lg-5 col-md-10">
            <!-- about-img -->
            <div class="about-img">
              <img src="@/assets/index_image/ALstructure.png" alt="">
            </div>
          </div>
          <div class="col-xl-5 col-lg-6 col-md-10">
            <div class="about-caption mb-50">
              <!-- Section Tittle -->
              <div class="section-tittle mb-50">
                <h2>
                  AssetsLint<br>
                  {{ $t("home.automatedResource") }}
                </h2>
              </div>
              <div class="single-offers mb-20">
                <div class="offers-cap">
                  <img src="@/assets/index_image/icon/notification1.svg" alt="">
                  <h3>
                    <a href="javascript:;">{{ $t("home.introduction") }}</a>
                  </h3>
                  <p>
                    <b>{{ $t("home.assetsLint") }}</b>
                    {{ $t("home.assetsLintText") }}
                  </p>
                </div>
              </div>
              <div class="single-offers">
                <div class="offers-cap">
                  <img src="@/assets/index_image/icon/notification1.svg" alt="">
                  <h3>
                    <a href="javascript:;">{{ $t("home.advantages") }}</a>
                  </h3>
                  <p>
                    <b>{{ $t("home.noCode") }}：</b>{{ $t("home.noCodeText")
                    }}<br>
                    <b>{{ $t("home.ruleCompleteness") }}：</b>{{ $t("home.ruleCompletenessText") }}<br>
                    <b>{{ $t("home.visualisation") }}：</b>{{ $t("home.visualisationText") }}<br>
                    <b>{{ $t("home.continuousIntegration") }}：</b>{{ $t("home.continuousIntegrationText") }}
                  </p>
                </div>
              </div>
              <!-- <router-link to="/helpguide" class="btn mt-20">{{
                $t("home.clickToHelpGuide")
              }}</router-link> -->
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- 首页模块3 结束 -->
    <div class="count-down-area section-padding border-bottom">
      <div class="container">
        <div class="row justify-content-center">
          <div class="col-xl-8 col-lg-9 col-md-12">
            <!-- Section Tittle -->
            <div class="section-tittle text-center mb-40">
              <h2>{{ $t("home.ruleEditor") }}</h2>
            </div>
          </div>
          <img src="@/assets/index_image/rule-editor.png" class="mb-55">
          <div class="text-center mb-40">
            <router-link to="/resourcedetect/rulecreate" class="btn">{{
              $t("home.goCreateRule")
            }}</router-link>
          </div>
        </div>
      </div>
    </div>
    <!-- 最底部区域 -->
    <!-- <div class="footer-bottom-area mt-100">
      <div class="container">
        <div class="footer-border">
          <div class="row">
            <div class="col-xl-12">
              <div class="footer-copy-right text-center">
                <p>
                  Copyright &copy; All Rights Reserved | AssetsLint
                  资源自动化检测平台
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div> -->
    <!-- 返回顶部组件 -->
    <el-tooltip placement="top" :content="$t('backToTop')">
      <back-to-top
        :visibility-height="300"
        :back-position="50"
        transition-name="fade"
      ></back-to-top>
    </el-tooltip>
  </div>
</template>

<script>
// 引入Jquery, Popper, Bootstrap
import '@/assets/js/vendor/modernizr-3.5.0.min.js'
import '@/assets/js/vendor/jquery-1.12.4.min.js'
import '@/assets/js/popper.min.js'
import 'bootstrap/dist/js/bootstrap.min'
import BackToTop from '@/components/BackToTop'

export default {
  components: { BackToTop },
  data() {
    return {
      dialogPlay: false,
      isVideoPause: false
    }
  },
  methods: {
    playVideo() {
      this.$message.warning("暂无视频");
      // this.dialogPlay = true;
      // if (this.isVideoPause) {
      //   this.$refs.dialogVideo.play();
      //   this.isVideoPause = false;
      // }
    },
    closeDialog() {
      this.$refs.dialogVideo.pause()
      this.isVideoPause = true
    }
  }
}
</script>

<style scoped src="bootstrap/dist/css/bootstrap.min.css"></style>
<style scoped src="@/assets/css/style.css"></style>
<style lang="scss" scoped>
.slider-area {
  position: relative !important;
}

::v-deep .el-dialog__body {
  padding: 10px;
}

a {
  text-decoration: none;
  user-select: none;
}

.single-cat {
  cursor: pointer;
  user-select: none;
}

// .slider-area {
//   font-family: "Mulish", sans-serif;
//   font-weight: normal;
//   font-style: normal;
//   font-size: 16px;

//   margin-top: -50px;
//   background: #00095e;
//   background: -moz-linear-gradient(left, #00095e 0%, #0c0c1f 100%);
//   background: -webkit-linear-gradient(left, #00095e 0%, #0c0c1f 100%);
//   background: linear-gradient(to right, #00095e 0%, #0c0c1f 100%);
//   filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00095e', endColorstr='#0c0c1f',GradientType=1 );

//   .slider-height {
//     height: 900px;
//     background-repeat: no-repeat;
//     background-position: center center;
//   }

//   .hero-caption {
//     position: relative;
//     top: -50px;
//     z-index: 2;

//     h1 {
//       color: #fff;
//       font-size: 64px;
//       font-weight: 400;
//       line-height: 1.3;
//       margin-bottom: 10px;
//     }
//     p {
//       margin-bottom: 29px;
//       padding-right: 16px;
//       color: #fff;
//       font-size: 18px;
//     }
//     .btn:not(:disabled):not(.disabled) {
//       cursor: pointer;
//     }
//     .hero-btn {
//       padding: 17px 27px;
//     }
//     .btn,
//     .video-icon a {
//       background: #ff4495;
//       background: -moz-linear-gradient(top, #ff4495 0%, #ff6d6d 100%);
//       background: -webkit-linear-gradient(top, #ff4495 0%, #ff6d6d 100%);
//       background: linear-gradient(to bottom, #ff4495 0%, #ff6d6d 100%);
//       filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ff4495', endColorstr='#ff6d6d',GradientType=0 );
//     }
//     .btn {
//       font-family: "Mulish", sans-serif;
//       text-transform: capitalize !important;
//       padding: 17px 31px;
//       color: #fff !important;
//       cursor: pointer;
//       display: inline-block;
//       font-size: 16px !important;
//       font-weight: 700 !important;
//       border-radius: 25px;
//       line-height: 1;
//       cursor: pointer;
//       -moz-user-select: none;
//       transition: color 0.4s linear;
//       position: relative;
//       z-index: 1;
//       border: 0;
//       overflow: hidden;
//     }
//     .btn:hover::before {
//       transform: scaleY(1);
//       z-index: -1;
//     }
//     .btn::before {
//       content: "";
//       position: absolute;
//       left: 0;
//       top: 0;
//       width: 101%;
//       height: 101%;
//       z-index: 1;
//       border-radius: 5px;
//       transition: transform 0.5s;
//       transition-timing-function: ease;
//       transform-origin: 0 0;
//       transition-timing-function: cubic-bezier(0.5, 1.6, 0.4, 0.7);
//       transform: scaleY(0);
//       border-radius: 0px;
//     }
//     .btn:hover::before {
//       transform: scaleX(1);
//       color: #fff !important;
//       z-index: -1;
//     }
//   }

//   .hero-shape {
//     position: absolute;
//     top: -100%;
//     right: -155px;
//   }
// }
</style>
